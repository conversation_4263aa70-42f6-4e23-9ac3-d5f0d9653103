package models

import (
	"time"

	"gorm.io/gorm"
)

// Product 商品模型
type Product struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	SKU             string         `json:"sku" gorm:"size:50;uniqueIndex;not null"`
	Name            string         `json:"name" gorm:"size:100;not null"`
	Description     string         `json:"description" gorm:"type:text"`
	Category        string         `json:"category" gorm:"size:50"`
	Unit            string         `json:"unit" gorm:"size:20;not null"` // 单位：个、箱、kg等
	Weight          float64        `json:"weight" gorm:"type:decimal(10,2)"`
	Volume          float64        `json:"volume" gorm:"type:decimal(10,2)"`
	Price           float64        `json:"price" gorm:"type:decimal(10,2)"`
	MinStock        int            `json:"min_stock" gorm:"default:0"` // 最小库存量
	MaxStock        int            `json:"max_stock" gorm:"default:0"` // 最大库存量
	ShelfLife       int            `json:"shelf_life"`                  // 保质期(天)
	SupplierID      uint           `json:"supplier_id"`
	Supplier        Supplier       `json:"supplier" gorm:"foreignKey:SupplierID"`
	Status          int            `json:"status" gorm:"default:1"` // 1: 启用, 0: 禁用
	BatchManagement bool           `json:"batch_management" gorm:"default:false"`  // 是否批次管理
	SerialManagement bool          `json:"serial_management" gorm:"default:false"` // 是否序列号管理
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "wms_product"
}

// ProductCreateRequest 商品创建请求
type ProductCreateRequest struct {
	SKU             string  `json:"sku" binding:"required,max=50"`
	Name            string  `json:"name" binding:"required,max=100"`
	Description     string  `json:"description"`
	Category        string  `json:"category" binding:"max=50"`
	Unit            string  `json:"unit" binding:"required,max=20"`
	Weight          float64 `json:"weight"`
	Volume          float64 `json:"volume"`
	Price           float64 `json:"price"`
	MinStock        int     `json:"min_stock"`
	MaxStock        int     `json:"max_stock"`
	ShelfLife       int     `json:"shelf_life"`
	SupplierID      uint    `json:"supplier_id" binding:"required"`
	BatchManagement bool    `json:"batch_management"`
	SerialManagement bool   `json:"serial_management"`
}

// ProductUpdateRequest 商品更新请求
type ProductUpdateRequest struct {
	Name            string  `json:"name" binding:"max=100"`
	Description     string  `json:"description"`
	Category        string  `json:"category" binding:"max=50"`
	Unit            string  `json:"unit" binding:"max=20"`
	Weight          float64 `json:"weight"`
	Volume          float64 `json:"volume"`
	Price           float64 `json:"price"`
	MinStock        int     `json:"min_stock"`
	MaxStock        int     `json:"max_stock"`
	ShelfLife       int     `json:"shelf_life"`
	SupplierID      uint    `json:"supplier_id"`
	Status          int     `json:"status"`
	BatchManagement bool    `json:"batch_management"`
	SerialManagement bool   `json:"serial_management"`
}

// ProductBatch 商品批次
type ProductBatch struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ProductID    uint           `json:"product_id" gorm:"index;not null"`
	Product      Product        `json:"product" gorm:"foreignKey:ProductID"`
	BatchNumber  string         `json:"batch_number" gorm:"size:50;not null"`
	ProductionDate *time.Time    `json:"production_date"`
	ExpiryDate   *time.Time     `json:"expiry_date"`
	Quantity     int            `json:"quantity" gorm:"default:0"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 正常, 0: 锁定
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ProductBatch) TableName() string {
	return "wms_product_batch"
}

// ProductSerial 商品序列号
type ProductSerial struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ProductID    uint           `json:"product_id" gorm:"index;not null"`
	Product      Product        `json:"product" gorm:"foreignKey:ProductID"`
	SerialNumber string         `json:"serial_number" gorm:"size:50;uniqueIndex;not null"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 在库, 2: 已出库, 0: 锁定
	LocationID   *uint          `json:"location_id"`
	Location     *Location      `json:"location" gorm:"foreignKey:LocationID"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ProductSerial) TableName() string {
	return "wms_product_serial"
}