package controllers

import (
	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 初始化各个控制器
	userController := NewUserController()
	productController := NewProductController()
	supplierController := NewSupplierController()
	customerController := NewCustomerController()
	warehouseController := NewWarehouseController()
	inventoryController := NewInventoryController()
	inboundController := NewInboundController()
	outboundController := NewOutboundController()
	reportController := NewReportController()
	integrationController := NewIntegrationController()

	// 注册各个控制器的路由
	userController.RegisterRoutes(r)
	productController.RegisterRoutes(r)
	supplierController.RegisterRoutes(r)
	customerController.RegisterRoutes(r)
	warehouseController.RegisterRoutes(r)
	inventoryController.RegisterRoutes(r)
	inboundController.RegisterRoutes(r)
	outboundController.RegisterRoutes(r)
	reportController.RegisterRoutes(r)
	integrationController.RegisterRoutes(r)

	// 设置公共路由
	api := r.Group("/api")
	{
		// 健康检查
		api.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "ok",
				"message": "服务运行正常",
			})
		})

		// 版本信息
		api.GET("/version", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"version":     "1.0.0",
				"build_time": "2023-06-01",
				"go_version": "1.20",
			})
		})
	}
}