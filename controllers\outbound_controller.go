package controllers

import (
	"net/http"
	"strconv"
	"time"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// OutboundController 出库控制器
type OutboundController struct {
	outboundService services.OutboundService
}

// NewOutboundController 创建出库控制器
func NewOutboundController() *OutboundController {
	return &OutboundController{}
}

// CreateSalesOrder 创建销售订单
func (ctrl *OutboundController) CreateSalesOrder(c *gin.Context) {
	var req models.SalesOrderCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	order, err := ctrl.outboundService.CreateSalesOrder(req)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    order,
	})
}

// ApproveSalesOrder 审核销售订单
func (ctrl *OutboundController) ApproveSalesOrder(c *gin.Context) {
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的订单ID"})
		return
	}

	var req struct {
		Approved bool   `json:"approved" binding:"required"`
		Remark   string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.outboundService.ApproveSalesOrder(uint(orderID), req.Approved, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "审核成功",
	})
}

// GetSalesOrderByID 根据ID获取销售订单
func (ctrl *OutboundController) GetSalesOrderByID(c *gin.Context) {
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的订单ID"})
		return
	}

	order, err := ctrl.outboundService.GetSalesOrderByID(uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    order,
	})
}

// GetSalesOrderByCode 根据编号获取销售订单
func (ctrl *OutboundController) GetSalesOrderByCode(c *gin.Context) {
	code := c.Param("code")

	order, err := ctrl.outboundService.GetSalesOrderByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    order,
	})
}

// ListSalesOrders 获取销售订单列表
func (ctrl *OutboundController) ListSalesOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	customerID, _ := strconv.Atoi(c.DefaultQuery("customerID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	orders, total, err := ctrl.outboundService.ListSalesOrders(page, pageSize, uint(customerID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  orders,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreateShipmentOrder 创建发货单
func (ctrl *OutboundController) CreateShipmentOrder(c *gin.Context) {
	var req models.ShipmentOrderCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	shipment, err := ctrl.outboundService.CreateShipmentOrder(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    shipment,
	})
}

// GetShipmentOrderByID 根据ID获取发货单
func (ctrl *OutboundController) GetShipmentOrderByID(c *gin.Context) {
	shipmentID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的发货单ID"})
		return
	}

	shipment, err := ctrl.outboundService.GetShipmentOrderByID(uint(shipmentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    shipment,
	})
}

// GetShipmentOrderByCode 根据编号获取发货单
func (ctrl *OutboundController) GetShipmentOrderByCode(c *gin.Context) {
	code := c.Param("code")

	shipment, err := ctrl.outboundService.GetShipmentOrderByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    shipment,
	})
}

// ListShipmentOrders 获取发货单列表
func (ctrl *OutboundController) ListShipmentOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	customerID, _ := strconv.Atoi(c.DefaultQuery("customerID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	shipments, total, err := ctrl.outboundService.ListShipmentOrders(page, pageSize, uint(customerID), uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  shipments,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreateWave 创建波次
func (ctrl *OutboundController) CreateWave(c *gin.Context) {
	var req models.WaveCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	wave, err := ctrl.outboundService.CreateWave(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    wave,
	})
}

// ReleaseWave 释放波次
func (ctrl *OutboundController) ReleaseWave(c *gin.Context) {
	waveID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的波次ID"})
		return
	}

	err = ctrl.outboundService.ReleaseWave(uint(waveID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "释放成功",
	})
}

// GetWaveByID 根据ID获取波次
func (ctrl *OutboundController) GetWaveByID(c *gin.Context) {
	waveID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的波次ID"})
		return
	}

	wave, err := ctrl.outboundService.GetWaveByID(uint(waveID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    wave,
	})
}

// GetWaveByCode 根据编号获取波次
func (ctrl *OutboundController) GetWaveByCode(c *gin.Context) {
	code := c.Param("code")

	wave, err := ctrl.outboundService.GetWaveByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    wave,
	})
}

// ListWaves 获取波次列表
func (ctrl *OutboundController) ListWaves(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	waves, total, err := ctrl.outboundService.ListWaves(page, pageSize, uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  waves,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetPickTaskByID 根据ID获取拣货任务
func (ctrl *OutboundController) GetPickTaskByID(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的拣货任务ID"})
		return
	}

	task, err := ctrl.outboundService.GetPickTaskByID(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    task,
	})
}

// ExecutePickTask 执行拣货任务
func (ctrl *OutboundController) ExecutePickTask(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的拣货任务ID"})
		return
	}

	var req struct {
		ActualQuantity int    `json:"actualQuantity" binding:"required"`
		Remark         string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.outboundService.ExecutePickTask(uint(taskID), req.ActualQuantity, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "执行成功",
	})
}

// ListPickTasks 获取拣货任务列表
func (ctrl *OutboundController) ListPickTasks(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	waveID, _ := strconv.Atoi(c.DefaultQuery("waveID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	tasks, total, err := ctrl.outboundService.ListPickTasks(page, pageSize, uint(waveID), uint(warehouseID), status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  tasks,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreatePackageOrder 创建包装单
func (ctrl *OutboundController) CreatePackageOrder(c *gin.Context) {
	var req models.PackageOrderCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	package_, err := ctrl.outboundService.CreatePackageOrder(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    package_,
	})
}

// CompletePackageOrder 完成包装单
func (ctrl *OutboundController) CompletePackageOrder(c *gin.Context) {
	packageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的包装单ID"})
		return
	}

	var req struct {
		TrackingNumber string `json:"trackingNumber" binding:"required"`
		Remark         string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.outboundService.CompletePackageOrder(uint(packageID), req.TrackingNumber, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "完成成功",
	})
}

// GetPackageOrderByID 根据ID获取包装单
func (ctrl *OutboundController) GetPackageOrderByID(c *gin.Context) {
	packageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的包装单ID"})
		return
	}

	package_, err := ctrl.outboundService.GetPackageOrderByID(uint(packageID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    package_,
	})
}

// GetPackageOrderByCode 根据编号获取包装单
func (ctrl *OutboundController) GetPackageOrderByCode(c *gin.Context) {
	code := c.Param("code")

	package_, err := ctrl.outboundService.GetPackageOrderByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    package_,
	})
}

// ListPackageOrders 获取包装单列表
func (ctrl *OutboundController) ListPackageOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	shipmentID, _ := strconv.Atoi(c.DefaultQuery("shipmentID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	packages, total, err := ctrl.outboundService.ListPackageOrders(page, pageSize, uint(shipmentID), uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  packages,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// RegisterRoutes 注册路由
func (ctrl *OutboundController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/outbound", middleware.JWTAuth())
		{
			// 销售订单
			auth.POST("/sales-order", ctrl.CreateSalesOrder)
			auth.POST("/sales-order/approve/:id", ctrl.ApproveSalesOrder)
			auth.GET("/sales-order/:id", ctrl.GetSalesOrderByID)
			auth.GET("/sales-order/code/:code", ctrl.GetSalesOrderByCode)
			auth.GET("/sales-order/list", ctrl.ListSalesOrders)

			// 发货单
			auth.POST("/shipment", ctrl.CreateShipmentOrder)
			auth.GET("/shipment/:id", ctrl.GetShipmentOrderByID)
			auth.GET("/shipment/code/:code", ctrl.GetShipmentOrderByCode)
			auth.GET("/shipment/list", ctrl.ListShipmentOrders)

			// 波次
			auth.POST("/wave", ctrl.CreateWave)
			auth.POST("/wave/release/:id", ctrl.ReleaseWave)
			auth.GET("/wave/:id", ctrl.GetWaveByID)
			auth.GET("/wave/code/:code", ctrl.GetWaveByCode)
			auth.GET("/wave/list", ctrl.ListWaves)

			// 拣货任务
			auth.GET("/pick/:id", ctrl.GetPickTaskByID)
			auth.POST("/pick/execute/:id", ctrl.ExecutePickTask)
			auth.GET("/pick/list", ctrl.ListPickTasks)

			// 包装单
			auth.POST("/package", ctrl.CreatePackageOrder)
			auth.POST("/package/complete/:id", ctrl.CompletePackageOrder)
			auth.GET("/package/:id", ctrl.GetPackageOrderByID)
			auth.GET("/package/code/:code", ctrl.GetPackageOrderByCode)
			auth.GET("/package/list", ctrl.ListPackageOrders)
		}
	}
}