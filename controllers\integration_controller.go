package controllers

import (
	"net/http"
	"strconv"

	"wms-gin/middleware"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// IntegrationController 系统集成控制器
type IntegrationController struct {
	// 这里可以注入需要的服务
	integrationService services.IntegrationService
}

// NewIntegrationController 创建系统集成控制器
func NewIntegrationController() *IntegrationController {
	return &IntegrationController{}
}

// ImportProducts 导入产品数据
func (ctrl *IntegrationController) ImportProducts(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请选择要上传的文件"})
		return
	}

	// 检查文件类型
	fileType := c.DefaultPostForm("fileType", "excel")
	if fileType != "excel" && fileType != "csv" {
		c.J<PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": "不支持的文件类型，仅支持excel和csv"})
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法打开文件"})
		return
	}
	defer src.Close()

	// 调用服务导入产品数据
	result, err := ctrl.integrationService.ImportProducts(src, fileType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "导入成功",
		"data":    result,
	})
}

// ExportProducts 导出产品数据
func (ctrl *IntegrationController) ExportProducts(c *gin.Context) {
	// 获取查询参数
	categoryID, _ := strconv.Atoi(c.DefaultQuery("categoryID", "0"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))
	sku := c.Query("sku")
	name := c.Query("name")
	fileType := c.DefaultQuery("fileType", "excel")

	if fileType != "excel" && fileType != "csv" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的文件类型，仅支持excel和csv"})
		return
	}

	// 调用服务导出产品数据
	filePath, fileName, err := ctrl.integrationService.ExportProducts(
		uint(categoryID),
		uint(supplierID),
		sku,
		name,
		fileType,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 设置响应头，让浏览器下载文件
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+fileName)

	if fileType == "excel" {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	} else {
		c.Header("Content-Type", "text/csv")
	}

	// 返回文件
	c.File(filePath)
}

// ImportInventory 导入库存数据
func (ctrl *IntegrationController) ImportInventory(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请选择要上传的文件"})
		return
	}

	// 检查文件类型
	fileType := c.DefaultPostForm("fileType", "excel")
	if fileType != "excel" && fileType != "csv" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的文件类型，仅支持excel和csv"})
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法打开文件"})
		return
	}
	defer src.Close()

	// 调用服务导入库存数据
	result, err := ctrl.integrationService.ImportInventory(src, fileType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "导入成功",
		"data":    result,
	})
}

// ExportInventory 导出库存数据
func (ctrl *IntegrationController) ExportInventory(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	zoneID, _ := strconv.Atoi(c.DefaultQuery("zoneID", "0"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	sku := c.Query("sku")
	fileType := c.DefaultQuery("fileType", "excel")

	if fileType != "excel" && fileType != "csv" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的文件类型，仅支持excel和csv"})
		return
	}

	// 调用服务导出库存数据
	filePath, fileName, err := ctrl.integrationService.ExportInventory(
		uint(warehouseID),
		uint(zoneID),
		uint(productID),
		sku,
		fileType,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 设置响应头，让浏览器下载文件
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+fileName)

	if fileType == "excel" {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	} else {
		c.Header("Content-Type", "text/csv")
	}

	// 返回文件
	c.File(filePath)
}

// SyncOrderStatus 同步订单状态到外部系统
func (ctrl *IntegrationController) SyncOrderStatus(c *gin.Context) {
	var req struct {
		OrderType string `json:"orderType" binding:"required,oneof=purchase sales"` // purchase 或 sales
		OrderID   uint   `json:"orderID" binding:"required"`
		System    string `json:"system" binding:"required"` // 外部系统标识
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用服务同步订单状态
	result, err := ctrl.integrationService.SyncOrderStatus(req.OrderType, req.OrderID, req.System)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "同步成功",
		"data":    result,
	})
}

// ReceiveExternalOrder 接收外部系统订单
func (ctrl *IntegrationController) ReceiveExternalOrder(c *gin.Context) {
	// 获取外部系统标识
	system := c.Param("system")

	// 解析请求体
	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用服务处理外部订单
	result, err := ctrl.integrationService.ReceiveExternalOrder(system, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "接收成功",
		"data":    result,
	})
}

// SendInventoryUpdate 发送库存更新到外部系统
func (ctrl *IntegrationController) SendInventoryUpdate(c *gin.Context) {
	var req struct {
		ProductIDs []uint `json:"productIDs" binding:"required"`
		System     string `json:"system" binding:"required"` // 外部系统标识
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用服务发送库存更新
	result, err := ctrl.integrationService.SendInventoryUpdate(req.ProductIDs, req.System)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "发送成功",
		"data":    result,
	})
}

// GetIntegrationLogs 获取集成日志
func (ctrl *IntegrationController) GetIntegrationLogs(c *gin.Context) {
	// 获取查询参数
	system := c.Query("system")
	module := c.Query("module")
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取集成日志
	logs, total, err := ctrl.integrationService.GetIntegrationLogs(
		page,
		pageSize,
		system,
		module,
		status,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  logs,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetIntegrationConfig 获取集成配置
func (ctrl *IntegrationController) GetIntegrationConfig(c *gin.Context) {
	// 获取系统标识
	system := c.Param("system")

	// 调用服务获取集成配置
	config, err := ctrl.integrationService.GetIntegrationConfig(system)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    config,
	})
}

// UpdateIntegrationConfig 更新集成配置
func (ctrl *IntegrationController) UpdateIntegrationConfig(c *gin.Context) {
	// 获取系统标识
	system := c.Param("system")

	// 解析请求体
	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用服务更新集成配置
	err := ctrl.integrationService.UpdateIntegrationConfig(system, config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
	})
}

// TestIntegrationConnection 测试集成连接
func (ctrl *IntegrationController) TestIntegrationConnection(c *gin.Context) {
	// 获取系统标识
	system := c.Param("system")

	// 调用服务测试集成连接
	result, err := ctrl.integrationService.TestIntegrationConnection(system)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "测试成功",
		"data":    result,
	})
}

// RegisterRoutes 注册路由
func (ctrl *IntegrationController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/integration", middleware.JWTAuth())
		{
			// 数据导入导出
			auth.POST("/import/products", ctrl.ImportProducts)
			auth.GET("/export/products", ctrl.ExportProducts)
			auth.POST("/import/inventory", ctrl.ImportInventory)
			auth.GET("/export/inventory", ctrl.ExportInventory)

			// 外部系统集成
			auth.POST("/sync/order-status", ctrl.SyncOrderStatus)
			auth.POST("/send/inventory-update", ctrl.SendInventoryUpdate)
			auth.GET("/logs", ctrl.GetIntegrationLogs)

			// 集成配置管理
			auth.GET("/config/:system", ctrl.GetIntegrationConfig)
			auth.PUT("/config/:system", ctrl.UpdateIntegrationConfig)
			auth.POST("/test-connection/:system", ctrl.TestIntegrationConnection)
		}

		// 外部系统回调接口（不需要认证，但可能需要其他安全措施）
		api.POST("/integration/webhook/:system", ctrl.ReceiveExternalOrder)
	}
}