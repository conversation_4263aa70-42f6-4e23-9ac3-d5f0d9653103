package services

import (
	"errors"
	"fmt"
	"time"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// OutboundService 出库服务
type OutboundService struct{}

// CreateSalesOrder 创建销售订单
func (s *OutboundService) CreateSalesOrder(req models.SalesOrderCreateRequest) (models.SalesOrder, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查客户是否存在
	var customer models.Customer
	result := tx.First(&customer, req.CustomerID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.SalesOrder{}, errors.New("客户不存在")
		}
		return models.SalesOrder{}, result.Error
	}

	// 生成销售订单编号
	orderCode := fmt.Sprintf("SO%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建销售订单
	salesOrder := models.SalesOrder{
		OrderCode:    orderCode,
		CustomerID:   req.CustomerID,
		OrderDate:    req.OrderDate,
		ExpectedDate: req.ExpectedDate,
		Status:       1, // 待审核
		Remark:       req.Remark,
	}

	result = tx.Create(&salesOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.SalesOrder{}, result.Error
	}

	// 创建销售订单明细
	for _, item := range req.Items {
		// 检查商品是否存在
		var product models.Product
		result := tx.First(&product, item.ProductID)
		if result.Error != nil {
			tx.Rollback()
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return models.SalesOrder{}, fmt.Errorf("商品ID %d 不存在", item.ProductID)
			}
			return models.SalesOrder{}, result.Error
		}

		// 创建订单明细
		orderItem := models.SalesOrderItem{
			SalesOrderID: salesOrder.ID,
			ProductID:    item.ProductID,
			Quantity:     item.Quantity,
			Price:        item.Price,
			Remark:       item.Remark,
		}

		result = tx.Create(&orderItem)
		if result.Error != nil {
			tx.Rollback()
			return models.SalesOrder{}, result.Error
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.SalesOrder{}, err
	}

	// 加载关联信息
	database.DB.Preload("Customer").Preload("Items").Preload("Items.Product").First(&salesOrder, salesOrder.ID)

	return salesOrder, nil
}

// ApproveSalesOrder 审核销售订单
func (s *OutboundService) ApproveSalesOrder(id uint, approved bool, remark string) error {
	var salesOrder models.SalesOrder

	// 查询销售订单
	result := database.DB.First(&salesOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("销售订单不存在")
		}
		return result.Error
	}

	// 检查订单状态
	if salesOrder.Status != 1 {
		return errors.New("只有待审核状态的订单可以审核")
	}

	// 更新订单状态
	if approved {
		salesOrder.Status = 2 // 已审核
	} else {
		salesOrder.Status = 5 // 已取消
	}

	salesOrder.ApproveRemark = remark
	salesOrder.ApprovedAt = time.Now()

	result = database.DB.Save(&salesOrder)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// GetSalesOrderByID 根据ID获取销售订单
func (s *OutboundService) GetSalesOrderByID(id uint) (models.SalesOrder, error) {
	var salesOrder models.SalesOrder

	result := database.DB.Preload("Customer").Preload("Items").Preload("Items.Product").First(&salesOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.SalesOrder{}, errors.New("销售订单不存在")
		}
		return models.SalesOrder{}, result.Error
	}

	return salesOrder, nil
}

// GetSalesOrderByCode 根据编号获取销售订单
func (s *OutboundService) GetSalesOrderByCode(code string) (models.SalesOrder, error) {
	var salesOrder models.SalesOrder

	result := database.DB.Preload("Customer").Preload("Items").Preload("Items.Product").Where("order_code = ?", code).First(&salesOrder)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.SalesOrder{}, errors.New("销售订单不存在")
		}
		return models.SalesOrder{}, result.Error
	}

	return salesOrder, nil
}

// ListSalesOrders 获取销售订单列表
func (s *OutboundService) ListSalesOrders(page, pageSize int, customerID uint, status int, startDate, endDate *time.Time) ([]models.SalesOrder, int64, error) {
	var salesOrders []models.SalesOrder
	var total int64

	db := database.DB.Model(&models.SalesOrder{}).Preload("Customer")

	// 添加查询条件
	if customerID != 0 {
		db = db.Where("customer_id = ?", customerID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	if startDate != nil {
		db = db.Where("order_date >= ?", startDate)
	}

	if endDate != nil {
		db = db.Where("order_date <= ?", endDate)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&salesOrders)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return salesOrders, total, nil
}

// CreateShipmentOrder 创建发货单
func (s *OutboundService) CreateShipmentOrder(req models.ShipmentOrderCreateRequest) (models.ShipmentOrder, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查销售订单是否存在
	var salesOrder models.SalesOrder
	result := tx.Preload("Items").First(&salesOrder, req.SalesOrderID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ShipmentOrder{}, errors.New("销售订单不存在")
		}
		return models.ShipmentOrder{}, result.Error
	}

	// 检查销售订单状态
	if salesOrder.Status != 2 {
		tx.Rollback()
		return models.ShipmentOrder{}, errors.New("只有已审核的销售订单可以创建发货单")
	}

	// 检查仓库是否存在
	var warehouse models.Warehouse
	result = tx.First(&warehouse, req.WarehouseID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ShipmentOrder{}, errors.New("仓库不存在")
		}
		return models.ShipmentOrder{}, result.Error
	}

	// 生成发货单编号
	shipmentCode := fmt.Sprintf("SH%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建发货单
	shipmentOrder := models.ShipmentOrder{
		ShipmentCode:  shipmentCode,
		SalesOrderID:  req.SalesOrderID,
		WarehouseID:   req.WarehouseID,
		ShipmentDate:  req.ShipmentDate,
		Address:       req.Address,
		Contact:       req.Contact,
		Phone:         req.Phone,
		Status:        1, // 待分配
		Remark:        req.Remark,
	}

	result = tx.Create(&shipmentOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.ShipmentOrder{}, result.Error
	}

	// 创建发货单明细
	for _, item := range req.Items {
		// 检查商品是否存在于销售订单中
		var found bool
		var orderItem models.SalesOrderItem
		for _, oi := range salesOrder.Items {
			if oi.ProductID == item.ProductID {
				found = true
				orderItem = oi
				break
			}
		}

		if !found {
			tx.Rollback()
			return models.ShipmentOrder{}, fmt.Errorf("商品ID %d 不在销售订单中", item.ProductID)
		}

		// 检查发货数量是否超过销售数量
		if item.Quantity > orderItem.Quantity {
			tx.Rollback()
			return models.ShipmentOrder{}, fmt.Errorf("商品ID %d 的发货数量超过销售数量", item.ProductID)
		}

		// 创建发货单明细
		shipmentItem := models.ShipmentOrderItem{
			ShipmentOrderID: shipmentOrder.ID,
			ProductID:       item.ProductID,
			Quantity:        item.Quantity,
			Status:          1, // 待拣货
			Remark:          item.Remark,
		}

		result = tx.Create(&shipmentItem)
		if result.Error != nil {
			tx.Rollback()
			return models.ShipmentOrder{}, result.Error
		}
	}

	// 更新销售订单状态
	salesOrder.Status = 3 // 待发货
	result = tx.Save(&salesOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.ShipmentOrder{}, result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.ShipmentOrder{}, err
	}

	// 加载关联信息
	database.DB.Preload("SalesOrder").Preload("SalesOrder.Customer").Preload("Warehouse").Preload("Items").Preload("Items.Product").First(&shipmentOrder, shipmentOrder.ID)

	return shipmentOrder, nil
}

// GetShipmentOrderByID 根据ID获取发货单
func (s *OutboundService) GetShipmentOrderByID(id uint) (models.ShipmentOrder, error) {
	var shipmentOrder models.ShipmentOrder

	result := database.DB.Preload("SalesOrder").Preload("SalesOrder.Customer").Preload("Warehouse").Preload("Items").Preload("Items.Product").First(&shipmentOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ShipmentOrder{}, errors.New("发货单不存在")
		}
		return models.ShipmentOrder{}, result.Error
	}

	return shipmentOrder, nil
}

// GetShipmentOrderByCode 根据编号获取发货单
func (s *OutboundService) GetShipmentOrderByCode(code string) (models.ShipmentOrder, error) {
	var shipmentOrder models.ShipmentOrder

	result := database.DB.Preload("SalesOrder").Preload("SalesOrder.Customer").Preload("Warehouse").Preload("Items").Preload("Items.Product").Where("shipment_code = ?", code).First(&shipmentOrder)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ShipmentOrder{}, errors.New("发货单不存在")
		}
		return models.ShipmentOrder{}, result.Error
	}

	return shipmentOrder, nil
}

// ListShipmentOrders 获取发货单列表
func (s *OutboundService) ListShipmentOrders(page, pageSize int, warehouseID uint, status int, startDate, endDate *time.Time) ([]models.ShipmentOrder, int64, error) {
	var shipmentOrders []models.ShipmentOrder
	var total int64

	db := database.DB.Model(&models.ShipmentOrder{}).Preload("SalesOrder").Preload("SalesOrder.Customer").Preload("Warehouse")

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	if startDate != nil {
		db = db.Where("shipment_date >= ?", startDate)
	}

	if endDate != nil {
		db = db.Where("shipment_date <= ?", endDate)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&shipmentOrders)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return shipmentOrders, total, nil
}

// CreateWave 创建波次
func (s *OutboundService) CreateWave(req models.WaveCreateRequest) (models.Wave, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查仓库是否存在
	var warehouse models.Warehouse
	result := tx.First(&warehouse, req.WarehouseID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Wave{}, errors.New("仓库不存在")
		}
		return models.Wave{}, result.Error
	}

	// 生成波次编号
	waveCode := fmt.Sprintf("WV%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建波次
	wave := models.Wave{
		WaveCode:    waveCode,
		WarehouseID: req.WarehouseID,
		WaveType:    req.WaveType,
		Status:      1, // 待分配
		Remark:      req.Remark,
	}

	result = tx.Create(&wave)
	if result.Error != nil {
		tx.Rollback()
		return models.Wave{}, result.Error
	}

	// 添加发货单到波次
	for _, shipmentID := range req.ShipmentIDs {
		// 检查发货单是否存在
		var shipment models.ShipmentOrder
		result := tx.First(&shipment, shipmentID)
		if result.Error != nil {
			tx.Rollback()
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return models.Wave{}, fmt.Errorf("发货单ID %d 不存在", shipmentID)
			}
			return models.Wave{}, result.Error
		}

		// 检查发货单状态
		if shipment.Status != 1 {
			tx.Rollback()
			return models.Wave{}, fmt.Errorf("发货单ID %d 状态不是待分配", shipmentID)
		}

		// 检查发货单是否属于该仓库
		if shipment.WarehouseID != req.WarehouseID {
			tx.Rollback()
			return models.Wave{}, fmt.Errorf("发货单ID %d 不属于该仓库", shipmentID)
		}

		// 创建波次发货单关联
		waveShipment := models.WaveShipment{
			WaveID:     wave.ID,
			ShipmentID: shipmentID,
		}

		result = tx.Create(&waveShipment)
		if result.Error != nil {
			tx.Rollback()
			return models.Wave{}, result.Error
		}

		// 更新发货单状态
		shipment.Status = 2 // 已分配
		result = tx.Save(&shipment)
		if result.Error != nil {
			tx.Rollback()
			return models.Wave{}, result.Error
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.Wave{}, err
	}

	// 加载关联信息
	database.DB.Preload("Warehouse").Preload("WaveShipments").Preload("WaveShipments.Shipment").First(&wave, wave.ID)

	return wave, nil
}

// GetWaveByID 根据ID获取波次
func (s *OutboundService) GetWaveByID(id uint) (models.Wave, error) {
	var wave models.Wave

	result := database.DB.Preload("Warehouse").Preload("WaveShipments").Preload("WaveShipments.Shipment").First(&wave, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Wave{}, errors.New("波次不存在")
		}
		return models.Wave{}, result.Error
	}

	return wave, nil
}

// GetWaveByCode 根据编号获取波次
func (s *OutboundService) GetWaveByCode(code string) (models.Wave, error) {
	var wave models.Wave

	result := database.DB.Preload("Warehouse").Preload("WaveShipments").Preload("WaveShipments.Shipment").Where("wave_code = ?", code).First(&wave)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Wave{}, errors.New("波次不存在")
		}
		return models.Wave{}, result.Error
	}

	return wave, nil
}

// ListWaves 获取波次列表
func (s *OutboundService) ListWaves(page, pageSize int, warehouseID uint, status int) ([]models.Wave, int64, error) {
	var waves []models.Wave
	var total int64

	db := database.DB.Model(&models.Wave{}).Preload("Warehouse")

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&waves)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return waves, total, nil
}

// CreatePickTasks 创建拣货任务
func (s *OutboundService) CreatePickTasks(waveID uint) ([]models.PickTask, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查波次是否存在
	var wave models.Wave
	result := tx.Preload("WaveShipments").First(&wave, waveID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("波次不存在")
		}
		return nil, result.Error
	}

	// 检查波次状态
	if wave.Status != 1 {
		tx.Rollback()
		return nil, errors.New("只有待分配状态的波次可以创建拣货任务")
	}

	// 获取波次中的所有发货单
	var shipmentIDs []uint
	for _, ws := range wave.WaveShipments {
		shipmentIDs = append(shipmentIDs, ws.ShipmentID)
	}

	// 获取所有发货单明细
	var shipmentItems []models.ShipmentOrderItem
	result = tx.Preload("Product").Where("shipment_order_id IN ? AND status = 1", shipmentIDs).Find(&shipmentItems)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 按商品ID分组，合并相同商品的拣货任务
	productMap := make(map[uint]models.PickTaskGroup)
	for _, item := range shipmentItems {
		group, exists := productMap[item.ProductID]
		if !exists {
			group = models.PickTaskGroup{
				ProductID: item.ProductID,
				Quantity:  0,
				Items:     []models.ShipmentOrderItem{},
			}
		}
		group.Quantity += item.Quantity
		group.Items = append(group.Items, item)
		productMap[item.ProductID] = group
	}

	var tasks []models.PickTask

	// 为每个商品创建拣货任务
	for _, group := range productMap {
		// 查找库存
		var inventories []models.Inventory
		result = tx.Preload("Location").Where("product_id = ? AND quantity > 0", group.ProductID).Order("created_at ASC").Find(&inventories)
		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		// 检查库存是否足够
		var totalStock int
		for _, inv := range inventories {
			totalStock += inv.Quantity
		}

		if totalStock < group.Quantity {
			tx.Rollback()
			return nil, fmt.Errorf("商品ID %d 库存不足，需要 %d，实际 %d", group.ProductID, group.Quantity, totalStock)
		}

		// 按库位分配拣货任务
		remainQuantity := group.Quantity
		for _, inv := range inventories {
			if remainQuantity <= 0 {
				break
			}

			pickQuantity := inv.Quantity
			if pickQuantity > remainQuantity {
				pickQuantity = remainQuantity
			}

			// 创建拣货任务
			task := models.PickTask{
				WaveID:     waveID,
				ProductID:  group.ProductID,
				LocationID: inv.LocationID,
				Quantity:   pickQuantity,
				Status:     1, // 待拣货
			}

			result = tx.Create(&task)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}

			tasks = append(tasks, task)
			remainQuantity -= pickQuantity
		}
	}

	// 更新波次状态
	wave.Status = 2 // 拣货中
	result = tx.Save(&wave)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 加载关联信息
	for i := range tasks {
		database.DB.Preload("Wave").Preload("Product").Preload("Location").First(&tasks[i], tasks[i].ID)
	}

	return tasks, nil
}

// ExecutePickTask 执行拣货任务
func (s *OutboundService) ExecutePickTask(id uint, actualQuantity int) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询拣货任务
	var task models.PickTask
	result := tx.Preload("Wave").First(&task, id)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("拣货任务不存在")
		}
		return result.Error
	}

	// 检查任务状态
	if task.Status != 1 {
		tx.Rollback()
		return errors.New("只有待拣货状态的任务可以执行")
	}

	// 检查实际拣货数量
	if actualQuantity > task.Quantity {
		tx.Rollback()
		return errors.New("实际拣货数量不能超过计划数量")
	}

	// 更新库存
	inventoryService := InventoryService{}
	err := inventoryService.UpdateInventory(task.ProductID, task.LocationID, -actualQuantity, "出库", fmt.Sprintf("波次号：%s", task.Wave.WaveCode))
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新拣货任务状态和实际数量
	task.ActualQuantity = actualQuantity
	task.Status = 2 // 已完成
	task.CompletedAt = time.Now()

	result = tx.Save(&task)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 检查波次中的所有拣货任务是否都已完成
	var count int64
	tx.Model(&models.PickTask{}).Where("wave_id = ? AND status = 1", task.WaveID).Count(&count)

	if count == 0 {
		// 所有拣货任务都已完成，更新波次状态
		var wave models.Wave
		result = tx.First(&wave, task.WaveID)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		wave.Status = 3 // 已拣货
		result = tx.Save(&wave)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		// 更新发货单明细状态
		// 获取波次中的所有发货单
		var waveShipments []models.WaveShipment
		result = tx.Where("wave_id = ?", task.WaveID).Find(&waveShipments)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		var shipmentIDs []uint
		for _, ws := range waveShipments {
			shipmentIDs = append(shipmentIDs, ws.ShipmentID)
		}

		// 更新发货单明细状态
		result = tx.Model(&models.ShipmentOrderItem{}).Where("shipment_order_id IN ? AND status = 1", shipmentIDs).Update("status", 2) // 已拣货
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetPickTaskByID 根据ID获取拣货任务
func (s *OutboundService) GetPickTaskByID(id uint) (models.PickTask, error) {
	var task models.PickTask

	result := database.DB.Preload("Wave").Preload("Product").Preload("Location").First(&task, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PickTask{}, errors.New("拣货任务不存在")
		}
		return models.PickTask{}, result.Error
	}

	return task, nil
}

// ListPickTasks 获取拣货任务列表
func (s *OutboundService) ListPickTasks(page, pageSize int, waveID uint, status int) ([]models.PickTask, int64, error) {
	var tasks []models.PickTask
	var total int64

	db := database.DB.Model(&models.PickTask{}).Preload("Wave").Preload("Product").Preload("Location")

	// 添加查询条件
	if waveID != 0 {
		db = db.Where("wave_id = ?", waveID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&tasks)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return tasks, total, nil
}

// CreatePackageOrder 创建包装单
func (s *OutboundService) CreatePackageOrder(req models.PackageOrderCreateRequest) (models.PackageOrder, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查发货单是否存在
	var shipment models.ShipmentOrder
	result := tx.Preload("Items").First(&shipment, req.ShipmentOrderID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PackageOrder{}, errors.New("发货单不存在")
		}
		return models.PackageOrder{}, result.Error
	}

	// 检查发货单状态
	if shipment.Status != 2 {
		tx.Rollback()
		return models.PackageOrder{}, errors.New("只有已分配状态的发货单可以创建包装单")
	}

	// 检查发货单明细是否都已拣货
	var count int64
	tx.Model(&models.ShipmentOrderItem{}).Where("shipment_order_id = ? AND status != 2", req.ShipmentOrderID).Count(&count)
	if count > 0 {
		tx.Rollback()
		return models.PackageOrder{}, errors.New("发货单中存在未拣货的商品")
	}

	// 生成包装单编号
	packageCode := fmt.Sprintf("PK%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建包装单
	packageOrder := models.PackageOrder{
		PackageCode:     packageCode,
		ShipmentOrderID: req.ShipmentOrderID,
		PackageType:     req.PackageType,
		Weight:          req.Weight,
		Volume:          req.Volume,
		Status:          1, // 待发货
		Remark:          req.Remark,
	}

	result = tx.Create(&packageOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.PackageOrder{}, result.Error
	}

	// 创建包装单明细
	for _, item := range req.Items {
		// 检查商品是否存在于发货单中
		var found bool
		var shipmentItem models.ShipmentOrderItem
		for _, si := range shipment.Items {
			if si.ProductID == item.ProductID {
				found = true
				shipmentItem = si
				break
			}
		}

		if !found {
			tx.Rollback()
			return models.PackageOrder{}, fmt.Errorf("商品ID %d 不在发货单中", item.ProductID)
		}

		// 检查包装数量是否超过发货数量
		if item.Quantity > shipmentItem.Quantity {
			tx.Rollback()
			return models.PackageOrder{}, fmt.Errorf("商品ID %d 的包装数量超过发货数量", item.ProductID)
		}

		// 创建包装单明细
		packageItem := models.PackageOrderItem{
			PackageOrderID: packageOrder.ID,
			ProductID:      item.ProductID,
			Quantity:       item.Quantity,
			Remark:         item.Remark,
		}

		result = tx.Create(&packageItem)
		if result.Error != nil {
			tx.Rollback()
			return models.PackageOrder{}, result.Error
		}
	}

	// 更新发货单状态
	shipment.Status = 3 // 待发货
	result = tx.Save(&shipment)
	if result.Error != nil {
		tx.Rollback()
		return models.PackageOrder{}, result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.PackageOrder{}, err
	}

	// 加载关联信息
	database.DB.Preload("ShipmentOrder").Preload("ShipmentOrder.SalesOrder").Preload("ShipmentOrder.SalesOrder.Customer").Preload("Items").Preload("Items.Product").First(&packageOrder, packageOrder.ID)

	return packageOrder, nil
}

// ShipPackageOrder 发货包装单
func (s *OutboundService) ShipPackageOrder(id uint, trackingNumber string, carrier string) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询包装单
	var packageOrder models.PackageOrder
	result := tx.Preload("ShipmentOrder").First(&packageOrder, id)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("包装单不存在")
		}
		return result.Error
	}

	// 检查包装单状态
	if packageOrder.Status != 1 {
		tx.Rollback()
		return errors.New("只有待发货状态的包装单可以发货")
	}

	// 更新包装单状态和物流信息
	packageOrder.Status = 2 // 已发货
	packageOrder.TrackingNumber = trackingNumber
	packageOrder.Carrier = carrier
	packageOrder.ShippedAt = time.Now()

	result = tx.Save(&packageOrder)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 检查发货单的所有包装单是否都已发货
	var count int64
	tx.Model(&models.PackageOrder{}).Where("shipment_order_id = ? AND status = 1", packageOrder.ShipmentOrderID).Count(&count)

	if count == 0 {
		// 所有包装单都已发货，更新发货单状态
		var shipment models.ShipmentOrder
		result = tx.Preload("SalesOrder").First(&shipment, packageOrder.ShipmentOrderID)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		shipment.Status = 4 // 已发货
		shipment.CompletedAt = time.Now()

		result = tx.Save(&shipment)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		// 更新销售订单状态
		var salesOrder models.SalesOrder
		result = tx.First(&salesOrder, shipment.SalesOrderID)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		salesOrder.Status = 4 // 已完成
		salesOrder.CompletedAt = time.Now()

		result = tx.Save(&salesOrder)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetPackageOrderByID 根据ID获取包装单
func (s *OutboundService) GetPackageOrderByID(id uint) (models.PackageOrder, error) {
	var packageOrder models.PackageOrder

	result := database.DB.Preload("ShipmentOrder").Preload("ShipmentOrder.SalesOrder").Preload("ShipmentOrder.SalesOrder.Customer").Preload("Items").Preload("Items.Product").First(&packageOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PackageOrder{}, errors.New("包装单不存在")
		}
		return models.PackageOrder{}, result.Error
	}

	return packageOrder, nil
}

// GetPackageOrderByCode 根据编号获取包装单
func (s *OutboundService) GetPackageOrderByCode(code string) (models.PackageOrder, error) {
	var packageOrder models.PackageOrder

	result := database.DB.Preload("ShipmentOrder").Preload("ShipmentOrder.SalesOrder").Preload("ShipmentOrder.SalesOrder.Customer").Preload("Items").Preload("Items.Product").Where("package_code = ?", code).First(&packageOrder)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PackageOrder{}, errors.New("包装单不存在")
		}
		return models.PackageOrder{}, result.Error
	}

	return packageOrder, nil
}

// ListPackageOrders 获取包装单列表
func (s *OutboundService) ListPackageOrders(page, pageSize int, shipmentOrderID uint, status int) ([]models.PackageOrder, int64, error) {
	var packageOrders []models.PackageOrder
	var total int64

	db := database.DB.Model(&models.PackageOrder{}).Preload("ShipmentOrder").Preload("ShipmentOrder.SalesOrder").Preload("ShipmentOrder.SalesOrder.Customer")

	// 添加查询条件
	if shipmentOrderID != 0 {
		db = db.Where("shipment_order_id = ?", shipmentOrderID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&packageOrders)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return packageOrders, total, nil
}