package controllers

import (
	"net/http"
	"strconv"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// SupplierController 供应商控制器
type SupplierController struct {
	supplierService services.SupplierService
}

// NewSupplierController 创建供应商控制器
func NewSupplierController() *SupplierController {
	return &SupplierController{}
}

// CreateSupplier 创建供应商
func (ctrl *SupplierController) CreateSupplier(c *gin.Context) {
	var req models.SupplierCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	supplier, err := ctrl.supplierService.CreateSupplier(req)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    supplier,
	})
}

// UpdateSupplier 更新供应商
func (ctrl *SupplierController) UpdateSupplier(c *gin.Context) {
	supplierID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的供应商ID"})
		return
	}

	var req models.SupplierUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	supplier, err := ctrl.supplierService.UpdateSupplier(uint(supplierID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    supplier,
	})
}

// GetSupplierByID 根据ID获取供应商
func (ctrl *SupplierController) GetSupplierByID(c *gin.Context) {
	supplierID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的供应商ID"})
		return
	}

	supplier, err := ctrl.supplierService.GetSupplierByID(uint(supplierID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    supplier,
	})
}

// GetSupplierByCode 根据编码获取供应商
func (ctrl *SupplierController) GetSupplierByCode(c *gin.Context) {
	code := c.Param("code")

	supplier, err := ctrl.supplierService.GetSupplierByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    supplier,
	})
}

// ListSuppliers 获取供应商列表
func (ctrl *SupplierController) ListSuppliers(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyword := c.Query("keyword")

	suppliers, total, err := ctrl.supplierService.ListSuppliers(page, pageSize, keyword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  suppliers,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteSupplier 删除供应商
func (ctrl *SupplierController) DeleteSupplier(c *gin.Context) {
	supplierID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的供应商ID"})
		return
	}

	err = ctrl.supplierService.DeleteSupplier(uint(supplierID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// RegisterRoutes 注册路由
func (ctrl *SupplierController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/supplier", middleware.JWTAuth())
		{
			auth.POST("", ctrl.CreateSupplier)
			auth.PUT("/:id", ctrl.UpdateSupplier)
			auth.GET("/:id", ctrl.GetSupplierByID)
			auth.GET("/code/:code", ctrl.GetSupplierByCode)
			auth.GET("/list", ctrl.ListSuppliers)
			auth.DELETE("/:id", ctrl.DeleteSupplier)
		}
	}
}