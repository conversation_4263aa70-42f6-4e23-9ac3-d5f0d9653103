package services

import (
	"errors"
	"fmt"
	"time"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// InboundService 入库服务
type InboundService struct{}

// CreatePurchaseOrder 创建采购订单
func (s *InboundService) CreatePurchaseOrder(req models.PurchaseOrderCreateRequest) (models.PurchaseOrder, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查供应商是否存在
	var supplier models.Supplier
	result := tx.First(&supplier, req.SupplierID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PurchaseOrder{}, errors.New("供应商不存在")
		}
		return models.PurchaseOrder{}, result.Error
	}

	// 生成采购订单编号
	orderCode := fmt.Sprintf("PO%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建采购订单
	purchaseOrder := models.PurchaseOrder{
		OrderCode:    orderCode,
		SupplierID:   req.SupplierID,
		OrderDate:    req.OrderDate,
		ExpectedDate: req.ExpectedDate,
		Status:       1, // 待审核
		Remark:       req.Remark,
	}

	result = tx.Create(&purchaseOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.PurchaseOrder{}, result.Error
	}

	// 创建采购订单明细
	for _, item := range req.Items {
		// 检查商品是否存在
		var product models.Product
		result := tx.First(&product, item.ProductID)
		if result.Error != nil {
			tx.Rollback()
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return models.PurchaseOrder{}, fmt.Errorf("商品ID %d 不存在", item.ProductID)
			}
			return models.PurchaseOrder{}, result.Error
		}

		// 创建订单明细
		orderItem := models.PurchaseOrderItem{
			PurchaseOrderID: purchaseOrder.ID,
			ProductID:       item.ProductID,
			Quantity:        item.Quantity,
			Price:           item.Price,
			Remark:          item.Remark,
		}

		result = tx.Create(&orderItem)
		if result.Error != nil {
			tx.Rollback()
			return models.PurchaseOrder{}, result.Error
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.PurchaseOrder{}, err
	}

	// 加载关联信息
	database.DB.Preload("Supplier").Preload("Items").Preload("Items.Product").First(&purchaseOrder, purchaseOrder.ID)

	return purchaseOrder, nil
}

// ApprovePurchaseOrder 审核采购订单
func (s *InboundService) ApprovePurchaseOrder(id uint, approved bool, remark string) error {
	var purchaseOrder models.PurchaseOrder

	// 查询采购订单
	result := database.DB.First(&purchaseOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("采购订单不存在")
		}
		return result.Error
	}

	// 检查订单状态
	if purchaseOrder.Status != 1 {
		return errors.New("只有待审核状态的订单可以审核")
	}

	// 更新订单状态
	if approved {
		purchaseOrder.Status = 2 // 已审核
	} else {
		purchaseOrder.Status = 5 // 已取消
	}

	purchaseOrder.ApproveRemark = remark
	purchaseOrder.ApprovedAt = time.Now()

	result = database.DB.Save(&purchaseOrder)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// GetPurchaseOrderByID 根据ID获取采购订单
func (s *InboundService) GetPurchaseOrderByID(id uint) (models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	result := database.DB.Preload("Supplier").Preload("Items").Preload("Items.Product").First(&purchaseOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PurchaseOrder{}, errors.New("采购订单不存在")
		}
		return models.PurchaseOrder{}, result.Error
	}

	return purchaseOrder, nil
}

// GetPurchaseOrderByCode 根据编号获取采购订单
func (s *InboundService) GetPurchaseOrderByCode(code string) (models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	result := database.DB.Preload("Supplier").Preload("Items").Preload("Items.Product").Where("order_code = ?", code).First(&purchaseOrder)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PurchaseOrder{}, errors.New("采购订单不存在")
		}
		return models.PurchaseOrder{}, result.Error
	}

	return purchaseOrder, nil
}

// ListPurchaseOrders 获取采购订单列表
func (s *InboundService) ListPurchaseOrders(page, pageSize int, supplierID uint, status int, startDate, endDate *time.Time) ([]models.PurchaseOrder, int64, error) {
	var purchaseOrders []models.PurchaseOrder
	var total int64

	db := database.DB.Model(&models.PurchaseOrder{}).Preload("Supplier")

	// 添加查询条件
	if supplierID != 0 {
		db = db.Where("supplier_id = ?", supplierID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	if startDate != nil {
		db = db.Where("order_date >= ?", startDate)
	}

	if endDate != nil {
		db = db.Where("order_date <= ?", endDate)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&purchaseOrders)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return purchaseOrders, total, nil
}

// CreateASN 创建到货通知单
func (s *InboundService) CreateASN(req models.ASNCreateRequest) (models.ASN, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查采购订单是否存在
	var purchaseOrder models.PurchaseOrder
	result := tx.Preload("Items").First(&purchaseOrder, req.PurchaseOrderID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ASN{}, errors.New("采购订单不存在")
		}
		return models.ASN{}, result.Error
	}

	// 检查采购订单状态
	if purchaseOrder.Status != 2 {
		tx.Rollback()
		return models.ASN{}, errors.New("只有已审核的采购订单可以创建到货通知")
	}

	// 生成到货通知单编号
	asnCode := fmt.Sprintf("ASN%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建到货通知单
	asn := models.ASN{
		ASNCode:         asnCode,
		PurchaseOrderID: req.PurchaseOrderID,
		ExpectedDate:    req.ExpectedDate,
		Status:          1, // 待收货
		Remark:          req.Remark,
	}

	result = tx.Create(&asn)
	if result.Error != nil {
		tx.Rollback()
		return models.ASN{}, result.Error
	}

	// 创建到货通知单明细
	for _, item := range req.Items {
		// 检查商品是否存在于采购订单中
		var found bool
		var orderItem models.PurchaseOrderItem
		for _, oi := range purchaseOrder.Items {
			if oi.ProductID == item.ProductID {
				found = true
				orderItem = oi
				break
			}
		}

		if !found {
			tx.Rollback()
			return models.ASN{}, fmt.Errorf("商品ID %d 不在采购订单中", item.ProductID)
		}

		// 检查到货数量是否超过采购数量
		if item.Quantity > orderItem.Quantity {
			tx.Rollback()
			return models.ASN{}, fmt.Errorf("商品ID %d 的到货数量超过采购数量", item.ProductID)
		}

		// 创建到货通知单明细
		asnItem := models.ASNItem{
			ASNID:     asn.ID,
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Remark:    item.Remark,
		}

		result = tx.Create(&asnItem)
		if result.Error != nil {
			tx.Rollback()
			return models.ASN{}, result.Error
		}
	}

	// 更新采购订单状态
	purchaseOrder.Status = 3 // 待收货
	result = tx.Save(&purchaseOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.ASN{}, result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.ASN{}, err
	}

	// 加载关联信息
	database.DB.Preload("PurchaseOrder").Preload("PurchaseOrder.Supplier").Preload("Items").Preload("Items.Product").First(&asn, asn.ID)

	return asn, nil
}

// GetASNByID 根据ID获取到货通知单
func (s *InboundService) GetASNByID(id uint) (models.ASN, error) {
	var asn models.ASN

	result := database.DB.Preload("PurchaseOrder").Preload("PurchaseOrder.Supplier").Preload("Items").Preload("Items.Product").First(&asn, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ASN{}, errors.New("到货通知单不存在")
		}
		return models.ASN{}, result.Error
	}

	return asn, nil
}

// GetASNByCode 根据编号获取到货通知单
func (s *InboundService) GetASNByCode(code string) (models.ASN, error) {
	var asn models.ASN

	result := database.DB.Preload("PurchaseOrder").Preload("PurchaseOrder.Supplier").Preload("Items").Preload("Items.Product").Where("asn_code = ?", code).First(&asn)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ASN{}, errors.New("到货通知单不存在")
		}
		return models.ASN{}, result.Error
	}

	return asn, nil
}

// ListASNs 获取到货通知单列表
func (s *InboundService) ListASNs(page, pageSize int, purchaseOrderID uint, status int, startDate, endDate *time.Time) ([]models.ASN, int64, error) {
	var asns []models.ASN
	var total int64

	db := database.DB.Model(&models.ASN{}).Preload("PurchaseOrder").Preload("PurchaseOrder.Supplier")

	// 添加查询条件
	if purchaseOrderID != 0 {
		db = db.Where("purchase_order_id = ?", purchaseOrderID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	if startDate != nil {
		db = db.Where("expected_date >= ?", startDate)
	}

	if endDate != nil {
		db = db.Where("expected_date <= ?", endDate)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&asns)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return asns, total, nil
}

// CreateReceiptOrder 创建入库单
func (s *InboundService) CreateReceiptOrder(req models.ReceiptOrderCreateRequest) (models.ReceiptOrder, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查到货通知单是否存在
	var asn models.ASN
	result := tx.Preload("Items").Preload("PurchaseOrder").First(&asn, req.ASNID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ReceiptOrder{}, errors.New("到货通知单不存在")
		}
		return models.ReceiptOrder{}, result.Error
	}

	// 检查到货通知单状态
	if asn.Status != 1 {
		tx.Rollback()
		return models.ReceiptOrder{}, errors.New("只有待收货状态的到货通知单可以创建入库单")
	}

	// 检查仓库是否存在
	var warehouse models.Warehouse
	result = tx.First(&warehouse, req.WarehouseID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ReceiptOrder{}, errors.New("仓库不存在")
		}
		return models.ReceiptOrder{}, result.Error
	}

	// 生成入库单编号
	receiptCode := fmt.Sprintf("RO%s%d", time.Now().Format("20060102"), time.Now().UnixNano()/1e6)

	// 创建入库单
	receiptOrder := models.ReceiptOrder{
		ReceiptCode:  receiptCode,
		ASNID:        req.ASNID,
		WarehouseID:  req.WarehouseID,
		ReceiptDate:  req.ReceiptDate,
		Status:       1, // 待上架
		Remark:       req.Remark,
	}

	result = tx.Create(&receiptOrder)
	if result.Error != nil {
		tx.Rollback()
		return models.ReceiptOrder{}, result.Error
	}

	// 创建入库单明细
	for _, item := range req.Items {
		// 检查商品是否存在于到货通知单中
		var found bool
		var asnItem models.ASNItem
		for _, ai := range asn.Items {
			if ai.ProductID == item.ProductID {
				found = true
				asnItem = ai
				break
			}
		}

		if !found {
			tx.Rollback()
			return models.ReceiptOrder{}, fmt.Errorf("商品ID %d 不在到货通知单中", item.ProductID)
		}

		// 检查实收数量是否超过到货数量
		if item.ActualQuantity > asnItem.Quantity {
			tx.Rollback()
			return models.ReceiptOrder{}, fmt.Errorf("商品ID %d 的实收数量超过到货数量", item.ProductID)
		}

		// 创建入库单明细
		receiptItem := models.ReceiptOrderItem{
			ReceiptOrderID: receiptOrder.ID,
			ProductID:      item.ProductID,
			ExpectedQuantity: asnItem.Quantity,
			ActualQuantity:   item.ActualQuantity,
			BatchNumber:      item.BatchNumber,
			ProductionDate:   item.ProductionDate,
			ExpiryDate:       item.ExpiryDate,
			Status:           1, // 待上架
			Remark:           item.Remark,
		}

		result = tx.Create(&receiptItem)
		if result.Error != nil {
			tx.Rollback()
			return models.ReceiptOrder{}, result.Error
		}

		// 如果商品启用批次管理，创建批次记录
		var product models.Product
		result = tx.First(&product, item.ProductID)
		if result.Error != nil {
			tx.Rollback()
			return models.ReceiptOrder{}, result.Error
		}

		if product.BatchManagement && item.BatchNumber != "" {
			// 检查批次是否已存在
			var count int64
			tx.Model(&models.ProductBatch{}).Where("product_id = ? AND batch_number = ?", item.ProductID, item.BatchNumber).Count(&count)

			if count == 0 {
				// 创建批次记录
				batch := models.ProductBatch{
					ProductID:      item.ProductID,
					BatchNumber:    item.BatchNumber,
					ProductionDate: item.ProductionDate,
					ExpiryDate:     item.ExpiryDate,
					Quantity:       item.ActualQuantity,
					Status:         1, // 正常
				}

				result = tx.Create(&batch)
				if result.Error != nil {
					tx.Rollback()
					return models.ReceiptOrder{}, result.Error
				}
			} else {
				// 更新批次数量
				result = tx.Model(&models.ProductBatch{}).Where("product_id = ? AND batch_number = ?", item.ProductID, item.BatchNumber).UpdateColumn("quantity", gorm.Expr("quantity + ?", item.ActualQuantity))
				if result.Error != nil {
					tx.Rollback()
					return models.ReceiptOrder{}, result.Error
				}
			}
		}
	}

	// 更新到货通知单状态
	asn.Status = 2 // 已收货
	asn.ReceivedAt = time.Now()
	result = tx.Save(&asn)
	if result.Error != nil {
		tx.Rollback()
		return models.ReceiptOrder{}, result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.ReceiptOrder{}, err
	}

	// 加载关联信息
	database.DB.Preload("ASN").Preload("ASN.PurchaseOrder").Preload("ASN.PurchaseOrder.Supplier").Preload("Warehouse").Preload("Items").Preload("Items.Product").First(&receiptOrder, receiptOrder.ID)

	return receiptOrder, nil
}

// GetReceiptOrderByID 根据ID获取入库单
func (s *InboundService) GetReceiptOrderByID(id uint) (models.ReceiptOrder, error) {
	var receiptOrder models.ReceiptOrder

	result := database.DB.Preload("ASN").Preload("ASN.PurchaseOrder").Preload("ASN.PurchaseOrder.Supplier").Preload("Warehouse").Preload("Items").Preload("Items.Product").First(&receiptOrder, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ReceiptOrder{}, errors.New("入库单不存在")
		}
		return models.ReceiptOrder{}, result.Error
	}

	return receiptOrder, nil
}

// GetReceiptOrderByCode 根据编号获取入库单
func (s *InboundService) GetReceiptOrderByCode(code string) (models.ReceiptOrder, error) {
	var receiptOrder models.ReceiptOrder

	result := database.DB.Preload("ASN").Preload("ASN.PurchaseOrder").Preload("ASN.PurchaseOrder.Supplier").Preload("Warehouse").Preload("Items").Preload("Items.Product").Where("receipt_code = ?", code).First(&receiptOrder)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ReceiptOrder{}, errors.New("入库单不存在")
		}
		return models.ReceiptOrder{}, result.Error
	}

	return receiptOrder, nil
}

// ListReceiptOrders 获取入库单列表
func (s *InboundService) ListReceiptOrders(page, pageSize int, warehouseID uint, status int, startDate, endDate *time.Time) ([]models.ReceiptOrder, int64, error) {
	var receiptOrders []models.ReceiptOrder
	var total int64

	db := database.DB.Model(&models.ReceiptOrder{}).Preload("ASN").Preload("ASN.PurchaseOrder").Preload("ASN.PurchaseOrder.Supplier").Preload("Warehouse")

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	if startDate != nil {
		db = db.Where("receipt_date >= ?", startDate)
	}

	if endDate != nil {
		db = db.Where("receipt_date <= ?", endDate)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&receiptOrders)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return receiptOrders, total, nil
}

// CreatePutawayTask 创建上架任务
func (s *InboundService) CreatePutawayTask(receiptOrderID uint, items []models.PutawayTaskCreateItem) ([]models.PutawayTask, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查入库单是否存在
	var receiptOrder models.ReceiptOrder
	result := tx.Preload("Items").First(&receiptOrder, receiptOrderID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("入库单不存在")
		}
		return nil, result.Error
	}

	// 检查入库单状态
	if receiptOrder.Status != 1 {
		tx.Rollback()
		return nil, errors.New("只有待上架状态的入库单可以创建上架任务")
	}

	var tasks []models.PutawayTask

	// 创建上架任务
	for _, item := range items {
		// 检查入库单明细是否存在
		var receiptItem models.ReceiptOrderItem
		result := tx.First(&receiptItem, item.ReceiptItemID)
		if result.Error != nil {
			tx.Rollback()
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("入库单明细ID %d 不存在", item.ReceiptItemID)
			}
			return nil, result.Error
		}

		// 检查入库单明细是否属于该入库单
		if receiptItem.ReceiptOrderID != receiptOrderID {
			tx.Rollback()
			return nil, fmt.Errorf("入库单明细ID %d 不属于该入库单", item.ReceiptItemID)
		}

		// 检查入库单明细状态
		if receiptItem.Status != 1 {
			tx.Rollback()
			return nil, fmt.Errorf("入库单明细ID %d 状态不是待上架", item.ReceiptItemID)
		}

		// 检查上架数量是否超过实收数量
		if item.Quantity > receiptItem.ActualQuantity {
			tx.Rollback()
			return nil, fmt.Errorf("入库单明细ID %d 的上架数量超过实收数量", item.ReceiptItemID)
		}

		// 检查库位是否存在
		var location models.Location
		result = tx.First(&location, item.LocationID)
		if result.Error != nil {
			tx.Rollback()
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("库位ID %d 不存在", item.LocationID)
			}
			return nil, result.Error
		}

		// 检查库位是否属于入库单的仓库
		var zone models.Zone
		result = tx.First(&zone, location.ZoneID)
		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		if zone.WarehouseID != receiptOrder.WarehouseID {
			tx.Rollback()
			return nil, fmt.Errorf("库位ID %d 不属于入库单的仓库", item.LocationID)
		}

		// 检查库位状态
		if location.Status != 1 {
			tx.Rollback()
			return nil, fmt.Errorf("库位ID %d 状态不是启用", item.LocationID)
		}

		// 检查库位容量是否足够
		if location.Used+item.Quantity > location.Capacity {
			tx.Rollback()
			return nil, fmt.Errorf("库位ID %d 容量不足", item.LocationID)
		}

		// 创建上架任务
		task := models.PutawayTask{
			ReceiptOrderID: receiptOrderID,
			ReceiptItemID:  item.ReceiptItemID,
			ProductID:      receiptItem.ProductID,
			LocationID:     item.LocationID,
			Quantity:       item.Quantity,
			Status:         1, // 待执行
			Remark:         item.Remark,
		}

		result = tx.Create(&task)
		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		tasks = append(tasks, task)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 加载关联信息
	for i := range tasks {
		database.DB.Preload("ReceiptOrder").Preload("ReceiptItem").Preload("Product").Preload("Location").First(&tasks[i], tasks[i].ID)
	}

	return tasks, nil
}

// ExecutePutawayTask 执行上架任务
func (s *InboundService) ExecutePutawayTask(id uint) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询上架任务
	var task models.PutawayTask
	result := tx.Preload("ReceiptOrder").Preload("ReceiptItem").First(&task, id)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("上架任务不存在")
		}
		return result.Error
	}

	// 检查任务状态
	if task.Status != 1 {
		tx.Rollback()
		return errors.New("只有待执行状态的上架任务可以执行")
	}

	// 更新库存
	inventoryService := InventoryService{}
	err := inventoryService.UpdateInventory(task.ProductID, task.LocationID, task.Quantity, "入库", fmt.Sprintf("入库单号：%s", task.ReceiptOrder.ReceiptCode))
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新上架任务状态
	task.Status = 2 // 已完成
	task.CompletedAt = time.Now()

	result = tx.Save(&task)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 更新入库单明细状态
	var receiptItem models.ReceiptOrderItem
	result = tx.First(&receiptItem, task.ReceiptItemID)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 检查是否所有上架任务都已完成
	var count int64
	tx.Model(&models.PutawayTask{}).Where("receipt_item_id = ? AND status = 1", task.ReceiptItemID).Count(&count)

	if count == 0 {
		// 所有上架任务都已完成，更新入库单明细状态
		receiptItem.Status = 2 // 已上架
		result = tx.Save(&receiptItem)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

		// 检查入库单所有明细是否都已上架
		tx.Model(&models.ReceiptOrderItem{}).Where("receipt_order_id = ? AND status = 1", task.ReceiptOrderID).Count(&count)

		if count == 0 {
			// 所有明细都已上架，更新入库单状态
			var receiptOrder models.ReceiptOrder
			result = tx.First(&receiptOrder, task.ReceiptOrderID)
			if result.Error != nil {
				tx.Rollback()
				return result.Error
			}

			receiptOrder.Status = 2 // 已完成
			receiptOrder.CompletedAt = time.Now()

			result = tx.Save(&receiptOrder)
			if result.Error != nil {
				tx.Rollback()
				return result.Error
			}

			// 更新采购订单状态
			var asn models.ASN
			result = tx.First(&asn, receiptOrder.ASNID)
			if result.Error != nil {
				tx.Rollback()
				return result.Error
			}

			var purchaseOrder models.PurchaseOrder
			result = tx.First(&purchaseOrder, asn.PurchaseOrderID)
			if result.Error != nil {
				tx.Rollback()
				return result.Error
			}

			purchaseOrder.Status = 4 // 已完成
			purchaseOrder.CompletedAt = time.Now()

			result = tx.Save(&purchaseOrder)
			if result.Error != nil {
				tx.Rollback()
				return result.Error
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetPutawayTaskByID 根据ID获取上架任务
func (s *InboundService) GetPutawayTaskByID(id uint) (models.PutawayTask, error) {
	var task models.PutawayTask

	result := database.DB.Preload("ReceiptOrder").Preload("ReceiptItem").Preload("Product").Preload("Location").First(&task, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.PutawayTask{}, errors.New("上架任务不存在")
		}
		return models.PutawayTask{}, result.Error
	}

	return task, nil
}

// ListPutawayTasks 获取上架任务列表
func (s *InboundService) ListPutawayTasks(page, pageSize int, receiptOrderID uint, status int) ([]models.PutawayTask, int64, error) {
	var tasks []models.PutawayTask
	var total int64

	db := database.DB.Model(&models.PutawayTask{}).Preload("ReceiptOrder").Preload("ReceiptItem").Preload("Product").Preload("Location")

	// 添加查询条件
	if receiptOrderID != 0 {
		db = db.Where("receipt_order_id = ?", receiptOrderID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&tasks)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return tasks, total, nil
}