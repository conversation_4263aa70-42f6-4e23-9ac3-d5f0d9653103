package controllers

import (
	"net/http"
	"strconv"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// ProductController 产品控制器
type ProductController struct {
	productService services.ProductService
}

// NewProductController 创建产品控制器
func NewProductController() *ProductController {
	return &ProductController{}
}

// CreateProduct 创建产品
func (ctrl *ProductController) CreateProduct(c *gin.Context) {
	var req models.ProductCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	product, err := ctrl.productService.CreateProduct(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    product,
	})
}

// UpdateProduct 更新产品
func (ctrl *ProductController) UpdateProduct(c *gin.Context) {
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的产品ID"})
		return
	}

	var req models.ProductUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	product, err := ctrl.productService.UpdateProduct(uint(productID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    product,
	})
}

// GetProductByID 根据ID获取产品
func (ctrl *ProductController) GetProductByID(c *gin.Context) {
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的产品ID"})
		return
	}

	product, err := ctrl.productService.GetProductByID(uint(productID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    product,
	})
}

// GetProductBySKU 根据SKU获取产品
func (ctrl *ProductController) GetProductBySKU(c *gin.Context) {
	sku := c.Param("sku")

	product, err := ctrl.productService.GetProductBySKU(sku)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    product,
	})
}

// ListProducts 获取产品列表
func (ctrl *ProductController) ListProducts(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyword := c.Query("keyword")
	categoryID, _ := strconv.Atoi(c.DefaultQuery("categoryID", "0"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))

	products, total, err := ctrl.productService.ListProducts(page, pageSize, keyword, uint(categoryID), uint(supplierID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  products,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteProduct 删除产品
func (ctrl *ProductController) DeleteProduct(c *gin.Context) {
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的产品ID"})
		return
	}

	err = ctrl.productService.DeleteProduct(uint(productID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// CreateProductBatch 创建产品批次
func (ctrl *ProductController) CreateProductBatch(c *gin.Context) {
	var req models.ProductBatchCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	batch, err := ctrl.productService.CreateProductBatch(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    batch,
	})
}

// CreateProductSerial 创建产品序列号
func (ctrl *ProductController) CreateProductSerial(c *gin.Context) {
	var req models.ProductSerialCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	serial, err := ctrl.productService.CreateProductSerial(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    serial,
	})
}

// RegisterRoutes 注册路由
func (ctrl *ProductController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/product", middleware.JWTAuth())
		{
			auth.POST("", ctrl.CreateProduct)
			auth.PUT("/:id", ctrl.UpdateProduct)
			auth.GET("/:id", ctrl.GetProductByID)
			auth.GET("/sku/:sku", ctrl.GetProductBySKU)
			auth.GET("/list", ctrl.ListProducts)
			auth.DELETE("/:id", ctrl.DeleteProduct)
			auth.POST("/batch", ctrl.CreateProductBatch)
			auth.POST("/serial", ctrl.CreateProductSerial)
		}
	}
}