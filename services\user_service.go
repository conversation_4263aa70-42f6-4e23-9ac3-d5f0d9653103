package services

import (
	"errors"
	"time"

	"wms-gin/internal/database"
	"wms-gin/middleware"
	"wms-gin/models"

	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct{}

// Login 用户登录
func (s *UserService) Login(req models.UserLoginRequest) (string, models.UserResponse, error) {
	var user models.User

	// 查询用户
	result := database.DB.Where("username = ?", req.Username).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return "", models.UserResponse{}, errors.New("用户不存在")
		}
		return "", models.UserResponse{}, result.Error
	}

	// 检查用户状态
	if user.Status != 1 {
		return "", models.UserResponse{}, errors.New("用户已被禁用")
	}

	// 验证密码
	if !user.CheckPassword(req.Password) {
		return "", models.UserResponse{}, errors.New("密码错误")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	database.DB.Save(&user)

	// 生成JWT令牌
	token, err := middleware.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return "", models.UserResponse{}, err
	}

	return token, user.ToUserResponse(), nil
}

// Register 用户注册
func (s *UserService) Register(req models.UserRegisterRequest) (models.UserResponse, error) {
	// 检查用户名是否已存在
	var count int64
	database.DB.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		return models.UserResponse{}, errors.New("用户名已存在")
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Password: req.Password, // 密码会在BeforeSave钩子中自动哈希
		Name:     req.Name,
		Email:    req.Email,
		Phone:    req.Phone,
		Role:     "user", // 默认角色为普通用户
		Status:   1,      // 默认启用
	}

	result := database.DB.Create(&user)
	if result.Error != nil {
		return models.UserResponse{}, result.Error
	}

	return user.ToUserResponse(), nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (models.UserResponse, error) {
	var user models.User

	result := database.DB.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.UserResponse{}, errors.New("用户不存在")
		}
		return models.UserResponse{}, result.Error
	}

	return user.ToUserResponse(), nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(id uint, req models.UserUpdateRequest) (models.UserResponse, error) {
	var user models.User

	// 查询用户
	result := database.DB.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.UserResponse{}, errors.New("用户不存在")
		}
		return models.UserResponse{}, result.Error
	}

	// 更新用户信息
	if req.Name != "" {
		user.Name = req.Name
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Password != "" {
		user.Password = req.Password // 密码会在BeforeSave钩子中自动哈希
	}

	result = database.DB.Save(&user)
	if result.Error != nil {
		return models.UserResponse{}, result.Error
	}

	return user.ToUserResponse(), nil
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(page, pageSize int, query string) ([]models.UserResponse, int64, error) {
	var users []models.User
	var total int64

	db := database.DB.Model(&models.User{})

	// 如果有查询条件，添加查询
	if query != "" {
		db = db.Where("username LIKE ? OR name LIKE ? OR email LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&users)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为响应格式
	userResponses := make([]models.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToUserResponse()
	}

	return userResponses, total, nil
}

// ChangeUserStatus 修改用户状态
func (s *UserService) ChangeUserStatus(id uint, status int) error {
	var user models.User

	// 查询用户
	result := database.DB.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return result.Error
	}

	// 更新状态
	user.Status = status
	result = database.DB.Save(&user)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// ChangeUserRole 修改用户角色
func (s *UserService) ChangeUserRole(id uint, role string) error {
	var user models.User

	// 查询用户
	result := database.DB.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return result.Error
	}

	// 更新角色
	user.Role = role
	result = database.DB.Save(&user)
	if result.Error != nil {
		return result.Error
	}

	return nil
}