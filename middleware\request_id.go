package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestIDKey 请求ID的上下文键
const RequestIDKey = "X-Request-ID"

// RequestID 中间件为每个请求生成唯一的请求ID
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从请求头获取请求ID
		requestID := c.GetHeader(RequestIDKey)

		// 如果请求头中没有请求ID，则生成一个新的
		if requestID == "" {
			requestID = uuid.New().String()
		}

		// 将请求ID添加到上下文和响应头中
		c.Set(RequestIDKey, requestID)
		c.Header(RequestIDKey, requestID)

		c.Next()
	}
}

// GetRequestID 从上下文中获取请求ID
func GetRequestID(c *gin.Context) string {
	requestID, exists := c.Get(RequestIDKey)
	if !exists {
		return ""
	}
	return requestID.(string)
}