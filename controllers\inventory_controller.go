package controllers

import (
	"net/http"
	"strconv"
	"time"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// InventoryController 库存控制器
type InventoryController struct {
	inventoryService services.InventoryService
}

// NewInventoryController 创建库存控制器
func NewInventoryController() *InventoryController {
	return &InventoryController{}
}

// GetInventory 获取库存信息
func (ctrl *InventoryController) GetInventory(c *gin.Context) {
	productID, err := strconv.ParseUint(c.Query("productID"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的商品ID"})
		return
	}

	locationID, err := strconv.ParseUint(c.Query("locationID"), 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "无效的库位ID"})
		return
	}

	inventory, err := ctrl.inventoryService.GetInventory(uint(productID), uint(locationID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    inventory,
	})
}

// ListInventory 获取库存列表
func (ctrl *InventoryController) ListInventory(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	zoneID, _ := strconv.Atoi(c.DefaultQuery("zoneID", "0"))

	inventories, total, err := ctrl.inventoryService.ListInventory(page, pageSize, uint(productID), uint(warehouseID), uint(zoneID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  inventories,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// UpdateInventory 调整库存
func (ctrl *InventoryController) UpdateInventory(c *gin.Context) {
	var req struct {
		ProductID  uint   `json:"productID" binding:"required"`
		LocationID uint   `json:"locationID" binding:"required"`
		Quantity   int    `json:"quantity" binding:"required"`
		Reason     string `json:"reason" binding:"required"`
		Remark     string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := ctrl.inventoryService.UpdateInventory(req.ProductID, req.LocationID, req.Quantity, req.Reason, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "调整成功",
	})
}

// MoveInventory 移动库存
func (ctrl *InventoryController) MoveInventory(c *gin.Context) {
	var req struct {
		ProductID      uint   `json:"productID" binding:"required"`
		FromLocationID uint   `json:"fromLocationID" binding:"required"`
		ToLocationID   uint   `json:"toLocationID" binding:"required"`
		Quantity       int    `json:"quantity" binding:"required"`
		Remark         string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := ctrl.inventoryService.MoveInventory(req.ProductID, req.FromLocationID, req.ToLocationID, req.Quantity, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "移动成功",
	})
}

// CreateInventoryCheck 创建库存盘点任务
func (ctrl *InventoryController) CreateInventoryCheck(c *gin.Context) {
	var req models.InventoryCheckCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	check, err := ctrl.inventoryService.CreateInventoryCheck(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    check,
	})
}

// GetInventoryCheckByID 根据ID获取库存盘点任务
func (ctrl *InventoryController) GetInventoryCheckByID(c *gin.Context) {
	checkID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的盘点任务ID"})
		return
	}

	check, err := ctrl.inventoryService.GetInventoryCheckByID(uint(checkID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    check,
	})
}

// ListInventoryChecks 获取库存盘点任务列表
func (ctrl *InventoryController) ListInventoryChecks(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	checks, total, err := ctrl.inventoryService.ListInventoryChecks(page, pageSize, uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  checks,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// UpdateInventoryCheckItem 更新库存盘点明细
func (ctrl *InventoryController) UpdateInventoryCheckItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的盘点明细ID"})
		return
	}

	var req struct {
		ActualQuantity int    `json:"actualQuantity" binding:"required"`
		Remark         string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.inventoryService.UpdateInventoryCheckItem(uint(itemID), req.ActualQuantity, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
	})
}

// CompleteInventoryCheck 完成库存盘点任务
func (ctrl *InventoryController) CompleteInventoryCheck(c *gin.Context) {
	checkID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的盘点任务ID"})
		return
	}

	var req struct {
		Remark string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.inventoryService.CompleteInventoryCheck(uint(checkID), req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "完成盘点成功",
	})
}

// ListInventoryAlerts 获取库存预警列表
func (ctrl *InventoryController) ListInventoryAlerts(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	alertType, _ := strconv.Atoi(c.DefaultQuery("alertType", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	alerts, total, err := ctrl.inventoryService.ListInventoryAlerts(page, pageSize, uint(warehouseID), alertType, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  alerts,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// ProcessInventoryAlert 处理库存预警
func (ctrl *InventoryController) ProcessInventoryAlert(c *gin.Context) {
	alertID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的预警ID"})
		return
	}

	var req struct {
		ProcessResult string `json:"processResult" binding:"required"`
		Remark        string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.inventoryService.ProcessInventoryAlert(uint(alertID), req.ProcessResult, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "处理成功",
	})
}

// ListInventoryLogs 获取库存日志列表
func (ctrl *InventoryController) ListInventoryLogs(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	locationID, _ := strconv.Atoi(c.DefaultQuery("locationID", "0"))
	operationType := c.Query("operationType")

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	logs, total, err := ctrl.inventoryService.ListInventoryLogs(page, pageSize, uint(productID), uint(locationID), operationType, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  logs,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// RegisterRoutes 注册路由
func (ctrl *InventoryController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/inventory", middleware.JWTAuth())
		{
			// 库存查询
			auth.GET("", ctrl.GetInventory)
			auth.GET("/list", ctrl.ListInventory)
			auth.GET("/log/list", ctrl.ListInventoryLogs)

			// 库存操作
			auth.POST("/update", ctrl.UpdateInventory)
			auth.POST("/move", ctrl.MoveInventory)

			// 库存盘点
			auth.POST("/check", ctrl.CreateInventoryCheck)
			auth.GET("/check/:id", ctrl.GetInventoryCheckByID)
			auth.GET("/check/list", ctrl.ListInventoryChecks)
			auth.PUT("/check/item/:id", ctrl.UpdateInventoryCheckItem)
			auth.POST("/check/complete/:id", ctrl.CompleteInventoryCheck)

			// 库存预警
			auth.GET("/alert/list", ctrl.ListInventoryAlerts)
			auth.POST("/alert/process/:id", ctrl.ProcessInventoryAlert)
		}
	}
}