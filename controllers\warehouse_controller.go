package controllers

import (
	"net/http"
	"strconv"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// WarehouseController 仓库控制器
type WarehouseController struct {
	warehouseService services.WarehouseService
}

// NewWarehouseController 创建仓库控制器
func NewWarehouseController() *WarehouseController {
	return &WarehouseController{}
}

// CreateWarehouse 创建仓库
func (ctrl *WarehouseController) CreateWarehouse(c *gin.Context) {
	var req models.WarehouseCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	warehouse, err := ctrl.warehouseService.CreateWarehouse(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    warehouse,
	})
}

// UpdateWarehouse 更新仓库
func (ctrl *WarehouseController) UpdateWarehouse(c *gin.Context) {
	warehouseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的仓库ID"})
		return
	}

	var req models.WarehouseUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	warehouse, err := ctrl.warehouseService.UpdateWarehouse(uint(warehouseID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    warehouse,
	})
}

// GetWarehouseByID 根据ID获取仓库
func (ctrl *WarehouseController) GetWarehouseByID(c *gin.Context) {
	warehouseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的仓库ID"})
		return
	}

	warehouse, err := ctrl.warehouseService.GetWarehouseByID(uint(warehouseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    warehouse,
	})
}

// GetWarehouseByCode 根据编码获取仓库
func (ctrl *WarehouseController) GetWarehouseByCode(c *gin.Context) {
	code := c.Param("code")

	warehouse, err := ctrl.warehouseService.GetWarehouseByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    warehouse,
	})
}

// ListWarehouses 获取仓库列表
func (ctrl *WarehouseController) ListWarehouses(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyword := c.Query("keyword")

	warehouses, total, err := ctrl.warehouseService.ListWarehouses(page, pageSize, keyword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  warehouses,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteWarehouse 删除仓库
func (ctrl *WarehouseController) DeleteWarehouse(c *gin.Context) {
	warehouseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的仓库ID"})
		return
	}

	err = ctrl.warehouseService.DeleteWarehouse(uint(warehouseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// CreateZone 创建库区
func (ctrl *WarehouseController) CreateZone(c *gin.Context) {
	var req models.ZoneCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	zone, err := ctrl.warehouseService.CreateZone(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    zone,
	})
}

// UpdateZone 更新库区
func (ctrl *WarehouseController) UpdateZone(c *gin.Context) {
	zoneID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库区ID"})
		return
	}

	var req models.ZoneUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	zone, err := ctrl.warehouseService.UpdateZone(uint(zoneID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    zone,
	})
}

// GetZoneByID 根据ID获取库区
func (ctrl *WarehouseController) GetZoneByID(c *gin.Context) {
	zoneID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库区ID"})
		return
	}

	zone, err := ctrl.warehouseService.GetZoneByID(uint(zoneID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    zone,
	})
}

// ListZones 获取库区列表
func (ctrl *WarehouseController) ListZones(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))

	zones, total, err := ctrl.warehouseService.ListZones(page, pageSize, uint(warehouseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  zones,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteZone 删除库区
func (ctrl *WarehouseController) DeleteZone(c *gin.Context) {
	zoneID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库区ID"})
		return
	}

	err = ctrl.warehouseService.DeleteZone(uint(zoneID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// CreateLocation 创建库位
func (ctrl *WarehouseController) CreateLocation(c *gin.Context) {
	var req models.LocationCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	location, err := ctrl.warehouseService.CreateLocation(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    location,
	})
}

// UpdateLocation 更新库位
func (ctrl *WarehouseController) UpdateLocation(c *gin.Context) {
	locationID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库位ID"})
		return
	}

	var req models.LocationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	location, err := ctrl.warehouseService.UpdateLocation(uint(locationID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    location,
	})
}

// GetLocationByID 根据ID获取库位
func (ctrl *WarehouseController) GetLocationByID(c *gin.Context) {
	locationID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库位ID"})
		return
	}

	location, err := ctrl.warehouseService.GetLocationByID(uint(locationID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    location,
	})
}

// GetLocationByCode 根据编码获取库位
func (ctrl *WarehouseController) GetLocationByCode(c *gin.Context) {
	code := c.Param("code")

	location, err := ctrl.warehouseService.GetLocationByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    location,
	})
}

// ListLocations 获取库位列表
func (ctrl *WarehouseController) ListLocations(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	zoneID, _ := strconv.Atoi(c.DefaultQuery("zoneID", "0"))
	locationType, _ := strconv.Atoi(c.DefaultQuery("locationType", "0"))

	locations, total, err := ctrl.warehouseService.ListLocations(page, pageSize, uint(zoneID), locationType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  locations,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteLocation 删除库位
func (ctrl *WarehouseController) DeleteLocation(c *gin.Context) {
	locationID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的库位ID"})
		return
	}

	err = ctrl.warehouseService.DeleteLocation(uint(locationID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// RegisterRoutes 注册路由
func (ctrl *WarehouseController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/warehouse", middleware.JWTAuth())
		{
			// 仓库管理
			auth.POST("", ctrl.CreateWarehouse)
			auth.PUT("/:id", ctrl.UpdateWarehouse)
			auth.GET("/:id", ctrl.GetWarehouseByID)
			auth.GET("/code/:code", ctrl.GetWarehouseByCode)
			auth.GET("/list", ctrl.ListWarehouses)
			auth.DELETE("/:id", ctrl.DeleteWarehouse)

			// 库区管理
			auth.POST("/zone", ctrl.CreateZone)
			auth.PUT("/zone/:id", ctrl.UpdateZone)
			auth.GET("/zone/:id", ctrl.GetZoneByID)
			auth.GET("/zone/list", ctrl.ListZones)
			auth.DELETE("/zone/:id", ctrl.DeleteZone)

			// 库位管理
			auth.POST("/location", ctrl.CreateLocation)
			auth.PUT("/location/:id", ctrl.UpdateLocation)
			auth.GET("/location/:id", ctrl.GetLocationByID)
			auth.GET("/location/code/:code", ctrl.GetLocationByCode)
			auth.GET("/location/list", ctrl.ListLocations)
			auth.DELETE("/location/:id", ctrl.DeleteLocation)
		}
	}
}