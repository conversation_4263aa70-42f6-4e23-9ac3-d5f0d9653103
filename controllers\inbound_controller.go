package controllers

import (
	"net/http"
	"strconv"
	"time"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// InboundController 入库控制器
type InboundController struct {
	inboundService services.InboundService
}

// NewInboundController 创建入库控制器
func NewInboundController() *InboundController {
	return &InboundController{}
}

// CreatePurchaseOrder 创建采购订单
func (ctrl *InboundController) CreatePurchaseOrder(c *gin.Context) {
	var req models.PurchaseOrderCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	order, err := ctrl.inboundService.CreatePurchaseOrder(req)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    order,
	})
}

// ApprovePurchaseOrder 审核采购订单
func (ctrl *InboundController) ApprovePurchaseOrder(c *gin.Context) {
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的订单ID"})
		return
	}

	var req struct {
		Approved bool   `json:"approved" binding:"required"`
		Remark   string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.inboundService.ApprovePurchaseOrder(uint(orderID), req.Approved, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "审核成功",
	})
}

// GetPurchaseOrderByID 根据ID获取采购订单
func (ctrl *InboundController) GetPurchaseOrderByID(c *gin.Context) {
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的订单ID"})
		return
	}

	order, err := ctrl.inboundService.GetPurchaseOrderByID(uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    order,
	})
}

// GetPurchaseOrderByCode 根据编号获取采购订单
func (ctrl *InboundController) GetPurchaseOrderByCode(c *gin.Context) {
	code := c.Param("code")

	order, err := ctrl.inboundService.GetPurchaseOrderByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    order,
	})
}

// ListPurchaseOrders 获取采购订单列表
func (ctrl *InboundController) ListPurchaseOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	orders, total, err := ctrl.inboundService.ListPurchaseOrders(page, pageSize, uint(supplierID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  orders,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreateASN 创建到货通知
func (ctrl *InboundController) CreateASN(c *gin.Context) {
	var req models.ASNCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	asn, err := ctrl.inboundService.CreateASN(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    asn,
	})
}

// GetASNByID 根据ID获取到货通知
func (ctrl *InboundController) GetASNByID(c *gin.Context) {
	asnID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的到货通知ID"})
		return
	}

	asn, err := ctrl.inboundService.GetASNByID(uint(asnID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    asn,
	})
}

// GetASNByCode 根据编号获取到货通知
func (ctrl *InboundController) GetASNByCode(c *gin.Context) {
	code := c.Param("code")

	asn, err := ctrl.inboundService.GetASNByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    asn,
	})
}

// ListASNs 获取到货通知列表
func (ctrl *InboundController) ListASNs(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	asns, total, err := ctrl.inboundService.ListASNs(page, pageSize, uint(supplierID), uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  asns,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreateReceiptOrder 创建收货单
func (ctrl *InboundController) CreateReceiptOrder(c *gin.Context) {
	var req models.ReceiptOrderCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	receipt, err := ctrl.inboundService.CreateReceiptOrder(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    receipt,
	})
}

// GetReceiptOrderByID 根据ID获取收货单
func (ctrl *InboundController) GetReceiptOrderByID(c *gin.Context) {
	receiptID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的收货单ID"})
		return
	}

	receipt, err := ctrl.inboundService.GetReceiptOrderByID(uint(receiptID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    receipt,
	})
}

// GetReceiptOrderByCode 根据编号获取收货单
func (ctrl *InboundController) GetReceiptOrderByCode(c *gin.Context) {
	code := c.Param("code")

	receipt, err := ctrl.inboundService.GetReceiptOrderByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    receipt,
	})
}

// ListReceiptOrders 获取收货单列表
func (ctrl *InboundController) ListReceiptOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	receipts, total, err := ctrl.inboundService.ListReceiptOrders(page, pageSize, uint(supplierID), uint(warehouseID), status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  receipts,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// CreatePutawayTask 创建上架任务
func (ctrl *InboundController) CreatePutawayTask(c *gin.Context) {
	var req models.PutawayTaskCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	task, err := ctrl.inboundService.CreatePutawayTask(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    task,
	})
}

// ExecutePutawayTask 执行上架任务
func (ctrl *InboundController) ExecutePutawayTask(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的上架任务ID"})
		return
	}

	var req struct {
		ActualQuantity int    `json:"actualQuantity" binding:"required"`
		Remark         string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ctrl.inboundService.ExecutePutawayTask(uint(taskID), req.ActualQuantity, req.Remark)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "执行成功",
	})
}

// GetPutawayTaskByID 根据ID获取上架任务
func (ctrl *InboundController) GetPutawayTaskByID(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的上架任务ID"})
		return
	}

	task, err := ctrl.inboundService.GetPutawayTaskByID(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    task,
	})
}

// ListPutawayTasks 获取上架任务列表
func (ctrl *InboundController) ListPutawayTasks(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	receiptID, _ := strconv.Atoi(c.DefaultQuery("receiptID", "0"))
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	tasks, total, err := ctrl.inboundService.ListPutawayTasks(page, pageSize, uint(receiptID), uint(warehouseID), status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  tasks,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// RegisterRoutes 注册路由
func (ctrl *InboundController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/inbound", middleware.JWTAuth())
		{
			// 采购订单
			auth.POST("/purchase-order", ctrl.CreatePurchaseOrder)
			auth.POST("/purchase-order/approve/:id", ctrl.ApprovePurchaseOrder)
			auth.GET("/purchase-order/:id", ctrl.GetPurchaseOrderByID)
			auth.GET("/purchase-order/code/:code", ctrl.GetPurchaseOrderByCode)
			auth.GET("/purchase-order/list", ctrl.ListPurchaseOrders)

			// 到货通知
			auth.POST("/asn", ctrl.CreateASN)
			auth.GET("/asn/:id", ctrl.GetASNByID)
			auth.GET("/asn/code/:code", ctrl.GetASNByCode)
			auth.GET("/asn/list", ctrl.ListASNs)

			// 收货单
			auth.POST("/receipt", ctrl.CreateReceiptOrder)
			auth.GET("/receipt/:id", ctrl.GetReceiptOrderByID)
			auth.GET("/receipt/code/:code", ctrl.GetReceiptOrderByCode)
			auth.GET("/receipt/list", ctrl.ListReceiptOrders)

			// 上架任务
			auth.POST("/putaway", ctrl.CreatePutawayTask)
			auth.POST("/putaway/execute/:id", ctrl.ExecutePutawayTask)
			auth.GET("/putaway/:id", ctrl.GetPutawayTaskByID)
			auth.GET("/putaway/list", ctrl.ListPutawayTasks)
		}
	}
}