package config

// AppConfig 应用配置结构体
type AppConfig struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Log      LogConfig      `mapstructure:"log"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	Mode         string `mapstructure:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type        string `mapstructure:"type"`
	Host        string `mapstructure:"host"`
	Port        int    `mapstructure:"port"`
	Username    string `mapstructure:"username"`
	Password    string `mapstructure:"password"`
	DBName      string `mapstructure:"dbname"`
	MaxIdleConn int    `mapstructure:"max_idle_conn"`
	MaxOpenConn int    `mapstructure:"max_open_conn"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// 全局配置变量
var GlobalConfig AppConfig

// InitConfig 初始化配置
func InitConfig() {
	// 这里可以使用viper等配置库来加载配置文件
	// 为简化示例，这里直接设置默认配置
	GlobalConfig = AppConfig{
		Server: ServerConfig{
			Port:         8080,
			ReadTimeout:  60,
			WriteTimeout: 60,
			Mode:         "debug",
		},
		Database: DatabaseConfig{
			Type:        "mysql",
			Host:        "localhost",
			Port:        3306,
			Username:    "root",
			Password:    "password",
			DBName:      "wms",
			MaxIdleConn: 10,
			MaxOpenConn: 100,
		},
		JWT: JWTConfig{
			Secret:     "wms-gin-secret-key",
			ExpireTime: 24 * 60 * 60, // 24小时
		},
		Log: LogConfig{
			Level:      "info",
			FilePath:   "./logs/wms.log",
			MaxSize:    100,
			MaxBackups: 10,
			MaxAge:     30,
			Compress:   true,
		},
	}
}