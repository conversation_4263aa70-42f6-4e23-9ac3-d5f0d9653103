package services

import (
	"errors"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// ProductService 商品服务
type ProductService struct{}

// CreateProduct 创建商品
func (s *ProductService) CreateProduct(req models.ProductCreateRequest) (models.Product, error) {
	// 检查SKU是否已存在
	var count int64
	database.DB.Model(&models.Product{}).Where("sku = ?", req.SKU).Count(&count)
	if count > 0 {
		return models.Product{}, errors.New("SKU已存在")
	}

	// 检查供应商是否存在
	var supplier models.Supplier
	result := database.DB.First(&supplier, req.SupplierID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Product{}, errors.New("供应商不存在")
		}
		return models.Product{}, result.Error
	}

	// 创建商品
	product := models.Product{
		SKU:              req.SKU,
		Name:             req.Name,
		Description:      req.Description,
		Category:         req.Category,
		Unit:             req.Unit,
		Weight:           req.Weight,
		Volume:           req.Volume,
		Price:            req.Price,
		MinStock:         req.MinStock,
		MaxStock:         req.MaxStock,
		ShelfLife:        req.ShelfLife,
		SupplierID:       req.SupplierID,
		Status:           1, // 默认启用
		BatchManagement:  req.BatchManagement,
		SerialManagement: req.SerialManagement,
	}

	result = database.DB.Create(&product)
	if result.Error != nil {
		return models.Product{}, result.Error
	}

	// 加载关联的供应商信息
	database.DB.Preload("Supplier").First(&product, product.ID)

	return product, nil
}

// UpdateProduct 更新商品
func (s *ProductService) UpdateProduct(id uint, req models.ProductUpdateRequest) (models.Product, error) {
	var product models.Product

	// 查询商品
	result := database.DB.First(&product, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Product{}, errors.New("商品不存在")
		}
		return models.Product{}, result.Error
	}

	// 如果更新供应商ID，检查供应商是否存在
	if req.SupplierID != 0 && req.SupplierID != product.SupplierID {
		var supplier models.Supplier
		result := database.DB.First(&supplier, req.SupplierID)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return models.Product{}, errors.New("供应商不存在")
			}
			return models.Product{}, result.Error
		}
		product.SupplierID = req.SupplierID
	}

	// 更新商品信息
	if req.Name != "" {
		product.Name = req.Name
	}
	if req.Description != "" {
		product.Description = req.Description
	}
	if req.Category != "" {
		product.Category = req.Category
	}
	if req.Unit != "" {
		product.Unit = req.Unit
	}
	if req.Weight != 0 {
		product.Weight = req.Weight
	}
	if req.Volume != 0 {
		product.Volume = req.Volume
	}
	if req.Price != 0 {
		product.Price = req.Price
	}
	if req.MinStock != 0 {
		product.MinStock = req.MinStock
	}
	if req.MaxStock != 0 {
		product.MaxStock = req.MaxStock
	}
	if req.ShelfLife != 0 {
		product.ShelfLife = req.ShelfLife
	}
	if req.Status != 0 {
		product.Status = req.Status
	}

	product.BatchManagement = req.BatchManagement
	product.SerialManagement = req.SerialManagement

	result = database.DB.Save(&product)
	if result.Error != nil {
		return models.Product{}, result.Error
	}

	// 加载关联的供应商信息
	database.DB.Preload("Supplier").First(&product, product.ID)

	return product, nil
}

// GetProductByID 根据ID获取商品
func (s *ProductService) GetProductByID(id uint) (models.Product, error) {
	var product models.Product

	result := database.DB.Preload("Supplier").First(&product, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Product{}, errors.New("商品不存在")
		}
		return models.Product{}, result.Error
	}

	return product, nil
}

// GetProductBySKU 根据SKU获取商品
func (s *ProductService) GetProductBySKU(sku string) (models.Product, error) {
	var product models.Product

	result := database.DB.Preload("Supplier").Where("sku = ?", sku).First(&product)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Product{}, errors.New("商品不存在")
		}
		return models.Product{}, result.Error
	}

	return product, nil
}

// ListProducts 获取商品列表
func (s *ProductService) ListProducts(page, pageSize int, query, category string, status int) ([]models.Product, int64, error) {
	var products []models.Product
	var total int64

	db := database.DB.Model(&models.Product{}).Preload("Supplier")

	// 添加查询条件
	if query != "" {
		db = db.Where("sku LIKE ? OR name LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if category != "" {
		db = db.Where("category = ?", category)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&products)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return products, total, nil
}

// DeleteProduct 删除商品
func (s *ProductService) DeleteProduct(id uint) error {
	var product models.Product

	// 查询商品
	result := database.DB.First(&product, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("商品不存在")
		}
		return result.Error
	}

	// 检查商品是否有关联的库存
	var count int64
	database.DB.Model(&models.Inventory{}).Where("product_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("商品存在库存记录，无法删除")
	}

	// 软删除商品
	result = database.DB.Delete(&product)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// CreateProductBatch 创建商品批次
func (s *ProductService) CreateProductBatch(productID uint, batchNumber string, productionDate, expiryDate *time.Time, quantity int) (models.ProductBatch, error) {
	// 检查商品是否存在
	var product models.Product
	result := database.DB.First(&product, productID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ProductBatch{}, errors.New("商品不存在")
		}
		return models.ProductBatch{}, result.Error
	}

	// 检查商品是否启用批次管理
	if !product.BatchManagement {
		return models.ProductBatch{}, errors.New("该商品未启用批次管理")
	}

	// 检查批次号是否已存在
	var count int64
	database.DB.Model(&models.ProductBatch{}).Where("product_id = ? AND batch_number = ?", productID, batchNumber).Count(&count)
	if count > 0 {
		return models.ProductBatch{}, errors.New("批次号已存在")
	}

	// 创建批次
	batch := models.ProductBatch{
		ProductID:      productID,
		BatchNumber:    batchNumber,
		ProductionDate: productionDate,
		ExpiryDate:     expiryDate,
		Quantity:       quantity,
		Status:         1, // 正常
	}

	result = database.DB.Create(&batch)
	if result.Error != nil {
		return models.ProductBatch{}, result.Error
	}

	// 加载关联的商品信息
	database.DB.Preload("Product").First(&batch, batch.ID)

	return batch, nil
}

// CreateProductSerial 创建商品序列号
func (s *ProductService) CreateProductSerial(productID uint, serialNumber string, locationID *uint) (models.ProductSerial, error) {
	// 检查商品是否存在
	var product models.Product
	result := database.DB.First(&product, productID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.ProductSerial{}, errors.New("商品不存在")
		}
		return models.ProductSerial{}, result.Error
	}

	// 检查商品是否启用序列号管理
	if !product.SerialManagement {
		return models.ProductSerial{}, errors.New("该商品未启用序列号管理")
	}

	// 检查序列号是否已存在
	var count int64
	database.DB.Model(&models.ProductSerial{}).Where("serial_number = ?", serialNumber).Count(&count)
	if count > 0 {
		return models.ProductSerial{}, errors.New("序列号已存在")
	}

	// 如果指定了库位，检查库位是否存在
	if locationID != nil {
		var location models.Location
		result := database.DB.First(&location, *locationID)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return models.ProductSerial{}, errors.New("库位不存在")
			}
			return models.ProductSerial{}, result.Error
		}
	}

	// 创建序列号
	serial := models.ProductSerial{
		ProductID:    productID,
		SerialNumber: serialNumber,
		LocationID:   locationID,
		Status:       1, // 在库
	}

	result = database.DB.Create(&serial)
	if result.Error != nil {
		return models.ProductSerial{}, result.Error
	}

	// 加载关联的商品和库位信息
	database.DB.Preload("Product").Preload("Location").First(&serial, serial.ID)

	return serial, nil
}