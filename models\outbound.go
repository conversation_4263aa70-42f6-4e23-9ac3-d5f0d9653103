package models

import (
	"time"

	"gorm.io/gorm"
)

// SalesOrder 销售订单
type SalesOrder struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	OrderNo      string         `json:"order_no" gorm:"size:50;uniqueIndex;not null"`
	CustomerID   uint           `json:"customer_id" gorm:"index;not null"`
	Customer     Customer       `json:"customer" gorm:"foreignKey:CustomerID"`
	OrderDate    time.Time      `json:"order_date"`
	ExpectedDate *time.Time     `json:"expected_date"` // 预计发货日期
	Status       int            `json:"status" gorm:"default:1"` // 1: 待审核, 2: 已审核, 3: 部分出库, 4: 已完成, 5: 已取消
	TotalAmount  float64        `json:"total_amount" gorm:"type:decimal(12,2);default:0"`
	Creator      string         `json:"creator" gorm:"size:50"`
	Approver     string         `json:"approver" gorm:"size:50"`
	ApproveTime  *time.Time     `json:"approve_time"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (SalesOrder) TableName() string {
	return "wms_sales_order"
}

// SalesOrderItem 销售订单明细
type SalesOrderItem struct {
	ID          uint       `json:"id" gorm:"primaryKey"`
	OrderID     uint       `json:"order_id" gorm:"index;not null"`
	Order       SalesOrder `json:"order" gorm:"foreignKey:OrderID"`
	ProductID   uint       `json:"product_id" gorm:"index;not null"`
	Product     Product    `json:"product" gorm:"foreignKey:ProductID"`
	Quantity    int        `json:"quantity" gorm:"default:0"`
	ShippedQty  int        `json:"shipped_qty" gorm:"default:0"`
	Price       float64    `json:"price" gorm:"type:decimal(10,2);default:0"`
	Amount      float64    `json:"amount" gorm:"type:decimal(12,2);default:0"`
	Remark      string     `json:"remark" gorm:"size:500"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (SalesOrderItem) TableName() string {
	return "wms_sales_order_item"
}

// ShipmentOrder 出库单
type ShipmentOrder struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ShipmentNo   string         `json:"shipment_no" gorm:"size:50;uniqueIndex;not null"`
	ShipmentType string         `json:"shipment_type" gorm:"size:50"` // 出库类型：销售出库、调拨出库、其他出库
	CustomerID   *uint          `json:"customer_id" gorm:"index"`
	Customer     *Customer      `json:"customer" gorm:"foreignKey:CustomerID"`
	OrderID      *uint          `json:"order_id" gorm:"index"` // 关联的销售订单ID，可能为空
	Order        *SalesOrder    `json:"order" gorm:"foreignKey:OrderID"`
	WarehouseID  uint           `json:"warehouse_id" gorm:"index;not null"`
	Warehouse    Warehouse      `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 待分配, 2: 待拣货, 3: 拣货中, 4: 待复核, 5: 待发货, 6: 已完成, 7: 已取消
	WaveID       *uint          `json:"wave_id" gorm:"index"` // 关联的波次ID，可能为空
	ShipmentDate *time.Time     `json:"shipment_date"`
	Creator      string         `json:"creator" gorm:"size:50"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ShipmentOrder) TableName() string {
	return "wms_shipment_order"
}

// ShipmentOrderItem 出库单明细
type ShipmentOrderItem struct {
	ID           uint          `json:"id" gorm:"primaryKey"`
	ShipmentID   uint          `json:"shipment_id" gorm:"index;not null"`
	Shipment     ShipmentOrder `json:"shipment" gorm:"foreignKey:ShipmentID"`
	OrderItemID  *uint         `json:"order_item_id" gorm:"index"`
	ProductID    uint          `json:"product_id" gorm:"index;not null"`
	Product      Product       `json:"product" gorm:"foreignKey:ProductID"`
	Quantity     int           `json:"quantity" gorm:"default:0"`
	PickedQty    int           `json:"picked_qty" gorm:"default:0"`
	CheckedQty   int           `json:"checked_qty" gorm:"default:0"`
	ShippedQty   int           `json:"shipped_qty" gorm:"default:0"`
	BatchPolicy  string        `json:"batch_policy" gorm:"size:50"` // 批次策略：FIFO, FEFO, LIFO等
	Status       int           `json:"status" gorm:"default:1"`     // 1: 待分配, 2: 待拣货, 3: 已拣货, 4: 已复核, 5: 已发货
	Remark       string        `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
}

// TableName 指定表名
func (ShipmentOrderItem) TableName() string {
	return "wms_shipment_order_item"
}

// Wave 波次
type Wave struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	WaveNo     string         `json:"wave_no" gorm:"size:50;uniqueIndex;not null"`
	WaveType   string         `json:"wave_type" gorm:"size:50"` // 波次类型：按订单、按商品、按区域等
	Status     int            `json:"status" gorm:"default:1"` // 1: 待分配, 2: 待拣货, 3: 拣货中, 4: 已完成, 5: 已取消
	Creator    string         `json:"creator" gorm:"size:50"`
	Remark     string         `json:"remark" gorm:"size:500"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Wave) TableName() string {
	return "wms_wave"
}

// WaveShipment 波次与出库单关联
type WaveShipment struct {
	ID         uint         `json:"id" gorm:"primaryKey"`
	WaveID     uint         `json:"wave_id" gorm:"index;not null"`
	Wave       Wave         `json:"wave" gorm:"foreignKey:WaveID"`
	ShipmentID uint         `json:"shipment_id" gorm:"index;not null"`
	Shipment   ShipmentOrder `json:"shipment" gorm:"foreignKey:ShipmentID"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
}

// TableName 指定表名
func (WaveShipment) TableName() string {
	return "wms_wave_shipment"
}

// PickTask 拣货任务
type PickTask struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	TaskNo        string         `json:"task_no" gorm:"size:50;uniqueIndex;not null"`
	WaveID        *uint          `json:"wave_id" gorm:"index"`
	Wave          *Wave          `json:"wave" gorm:"foreignKey:WaveID"`
	ShipmentID    uint           `json:"shipment_id" gorm:"index;not null"`
	Shipment      ShipmentOrder  `json:"shipment" gorm:"foreignKey:ShipmentID"`
	ShipmentItemID uint          `json:"shipment_item_id" gorm:"index;not null"`
	ShipmentItem  ShipmentOrderItem `json:"shipment_item" gorm:"foreignKey:ShipmentItemID"`
	ProductID     uint           `json:"product_id" gorm:"index;not null"`
	Product       Product        `json:"product" gorm:"foreignKey:ProductID"`
	LocationID    uint           `json:"location_id" gorm:"index;not null"`
	Location      Location       `json:"location" gorm:"foreignKey:LocationID"`
	BatchID       *uint          `json:"batch_id" gorm:"index"`
	Batch         *ProductBatch  `json:"batch" gorm:"foreignKey:BatchID"`
	Quantity      int            `json:"quantity" gorm:"default:0"`
	PickedQty     int            `json:"picked_qty" gorm:"default:0"`
	Status        int            `json:"status" gorm:"default:1"` // 1: 待拣货, 2: 已拣货
	Picker        string         `json:"picker" gorm:"size:50"`
	PickTime      *time.Time     `json:"pick_time"`
	Remark        string         `json:"remark" gorm:"size:500"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (PickTask) TableName() string {
	return "wms_pick_task"
}

// PackageOrder 包装单
type PackageOrder struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	PackageNo    string         `json:"package_no" gorm:"size:50;uniqueIndex;not null"`
	ShipmentID   uint           `json:"shipment_id" gorm:"index;not null"`
	Shipment     ShipmentOrder  `json:"shipment" gorm:"foreignKey:ShipmentID"`
	PackageType  string         `json:"package_type" gorm:"size:50"` // 包装类型
	Weight       float64        `json:"weight" gorm:"type:decimal(10,2);default:0"`
	Volume       float64        `json:"volume" gorm:"type:decimal(10,2);default:0"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 待包装, 2: 已包装, 3: 已发货
	Packer       string         `json:"packer" gorm:"size:50"`
	PackTime     *time.Time     `json:"pack_time"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (PackageOrder) TableName() string {
	return "wms_package_order"
}

// PackageOrderItem 包装单明细
type PackageOrderItem struct {
	ID           uint         `json:"id" gorm:"primaryKey"`
	PackageID    uint         `json:"package_id" gorm:"index;not null"`
	Package      PackageOrder `json:"package" gorm:"foreignKey:PackageID"`
	ShipmentItemID uint        `json:"shipment_item_id" gorm:"index;not null"`
	ShipmentItem ShipmentOrderItem `json:"shipment_item" gorm:"foreignKey:ShipmentItemID"`
	ProductID    uint         `json:"product_id" gorm:"index;not null"`
	Product      Product      `json:"product" gorm:"foreignKey:ProductID"`
	BatchID      *uint        `json:"batch_id" gorm:"index"`
	Batch        *ProductBatch `json:"batch" gorm:"foreignKey:BatchID"`
	SerialID     *uint        `json:"serial_id" gorm:"index"`
	Serial       *ProductSerial `json:"serial" gorm:"foreignKey:SerialID"`
	Quantity     int          `json:"quantity" gorm:"default:0"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
}

// TableName 指定表名
func (PackageOrderItem) TableName() string {
	return "wms_package_order_item"
}