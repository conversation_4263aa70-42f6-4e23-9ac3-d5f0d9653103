package services

import (
	"errors"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// WarehouseService 仓库服务
type WarehouseService struct{}

// CreateWarehouse 创建仓库
func (s *WarehouseService) CreateWarehouse(req models.WarehouseCreateRequest) (models.Warehouse, error) {
	// 检查仓库编码是否已存在
	var count int64
	database.DB.Model(&models.Warehouse{}).Where("code = ?", req.Code).Count(&count)
	if count > 0 {
		return models.Warehouse{}, errors.New("仓库编码已存在")
	}

	// 创建仓库
	warehouse := models.Warehouse{
		Code:        req.Code,
		Name:        req.Name,
		Address:     req.Address,
		Contact:     req.Contact,
		Phone:       req.Phone,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	result := database.DB.Create(&warehouse)
	if result.Error != nil {
		return models.Warehouse{}, result.Error
	}

	return warehouse, nil
}

// UpdateWarehouse 更新仓库
func (s *WarehouseService) UpdateWarehouse(id uint, req models.WarehouseUpdateRequest) (models.Warehouse, error) {
	var warehouse models.Warehouse

	// 查询仓库
	result := database.DB.First(&warehouse, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Warehouse{}, errors.New("仓库不存在")
		}
		return models.Warehouse{}, result.Error
	}

	// 如果更新编码，检查编码是否已存在
	if req.Code != "" && req.Code != warehouse.Code {
		var count int64
		database.DB.Model(&models.Warehouse{}).Where("code = ? AND id != ?", req.Code, id).Count(&count)
		if count > 0 {
			return models.Warehouse{}, errors.New("仓库编码已存在")
		}
		warehouse.Code = req.Code
	}

	// 更新仓库信息
	if req.Name != "" {
		warehouse.Name = req.Name
	}
	if req.Address != "" {
		warehouse.Address = req.Address
	}
	if req.Contact != "" {
		warehouse.Contact = req.Contact
	}
	if req.Phone != "" {
		warehouse.Phone = req.Phone
	}
	if req.Description != "" {
		warehouse.Description = req.Description
	}
	if req.Status != 0 {
		warehouse.Status = req.Status
	}

	result = database.DB.Save(&warehouse)
	if result.Error != nil {
		return models.Warehouse{}, result.Error
	}

	return warehouse, nil
}

// GetWarehouseByID 根据ID获取仓库
func (s *WarehouseService) GetWarehouseByID(id uint) (models.Warehouse, error) {
	var warehouse models.Warehouse

	result := database.DB.First(&warehouse, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Warehouse{}, errors.New("仓库不存在")
		}
		return models.Warehouse{}, result.Error
	}

	return warehouse, nil
}

// GetWarehouseByCode 根据编码获取仓库
func (s *WarehouseService) GetWarehouseByCode(code string) (models.Warehouse, error) {
	var warehouse models.Warehouse

	result := database.DB.Where("code = ?", code).First(&warehouse)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Warehouse{}, errors.New("仓库不存在")
		}
		return models.Warehouse{}, result.Error
	}

	return warehouse, nil
}

// ListWarehouses 获取仓库列表
func (s *WarehouseService) ListWarehouses(page, pageSize int, query string, status int) ([]models.Warehouse, int64, error) {
	var warehouses []models.Warehouse
	var total int64

	db := database.DB.Model(&models.Warehouse{})

	// 添加查询条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR address LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&warehouses)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return warehouses, total, nil
}

// DeleteWarehouse 删除仓库
func (s *WarehouseService) DeleteWarehouse(id uint) error {
	var warehouse models.Warehouse

	// 查询仓库
	result := database.DB.First(&warehouse, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("仓库不存在")
		}
		return result.Error
	}

	// 检查仓库是否有关联的区域
	var count int64
	database.DB.Model(&models.Zone{}).Where("warehouse_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("仓库存在关联区域，无法删除")
	}

	// 软删除仓库
	result = database.DB.Delete(&warehouse)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// CreateZone 创建区域
func (s *WarehouseService) CreateZone(req models.ZoneCreateRequest) (models.Zone, error) {
	// 检查仓库是否存在
	var warehouse models.Warehouse
	result := database.DB.First(&warehouse, req.WarehouseID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Zone{}, errors.New("仓库不存在")
		}
		return models.Zone{}, result.Error
	}

	// 检查区域编码是否已存在于该仓库
	var count int64
	database.DB.Model(&models.Zone{}).Where("warehouse_id = ? AND code = ?", req.WarehouseID, req.Code).Count(&count)
	if count > 0 {
		return models.Zone{}, errors.New("区域编码已存在于该仓库")
	}

	// 创建区域
	zone := models.Zone{
		WarehouseID: req.WarehouseID,
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	result = database.DB.Create(&zone)
	if result.Error != nil {
		return models.Zone{}, result.Error
	}

	// 加载关联的仓库信息
	database.DB.Preload("Warehouse").First(&zone, zone.ID)

	return zone, nil
}

// UpdateZone 更新区域
func (s *WarehouseService) UpdateZone(id uint, req models.ZoneUpdateRequest) (models.Zone, error) {
	var zone models.Zone

	// 查询区域
	result := database.DB.Preload("Warehouse").First(&zone, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Zone{}, errors.New("区域不存在")
		}
		return models.Zone{}, result.Error
	}

	// 如果更新编码，检查编码是否已存在于该仓库
	if req.Code != "" && req.Code != zone.Code {
		var count int64
		database.DB.Model(&models.Zone{}).Where("warehouse_id = ? AND code = ? AND id != ?", zone.WarehouseID, req.Code, id).Count(&count)
		if count > 0 {
			return models.Zone{}, errors.New("区域编码已存在于该仓库")
		}
		zone.Code = req.Code
	}

	// 更新区域信息
	if req.Name != "" {
		zone.Name = req.Name
	}
	if req.Description != "" {
		zone.Description = req.Description
	}
	if req.Status != 0 {
		zone.Status = req.Status
	}

	result = database.DB.Save(&zone)
	if result.Error != nil {
		return models.Zone{}, result.Error
	}

	return zone, nil
}

// GetZoneByID 根据ID获取区域
func (s *WarehouseService) GetZoneByID(id uint) (models.Zone, error) {
	var zone models.Zone

	result := database.DB.Preload("Warehouse").First(&zone, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Zone{}, errors.New("区域不存在")
		}
		return models.Zone{}, result.Error
	}

	return zone, nil
}

// ListZones 获取区域列表
func (s *WarehouseService) ListZones(warehouseID uint, page, pageSize int, query string, status int) ([]models.Zone, int64, error) {
	var zones []models.Zone
	var total int64

	db := database.DB.Model(&models.Zone{}).Preload("Warehouse")

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&zones)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return zones, total, nil
}

// DeleteZone 删除区域
func (s *WarehouseService) DeleteZone(id uint) error {
	var zone models.Zone

	// 查询区域
	result := database.DB.First(&zone, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("区域不存在")
		}
		return result.Error
	}

	// 检查区域是否有关联的库位
	var count int64
	database.DB.Model(&models.Location{}).Where("zone_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("区域存在关联库位，无法删除")
	}

	// 软删除区域
	result = database.DB.Delete(&zone)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// CreateLocation 创建库位
func (s *WarehouseService) CreateLocation(req models.LocationCreateRequest) (models.Location, error) {
	// 检查区域是否存在
	var zone models.Zone
	result := database.DB.First(&zone, req.ZoneID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Location{}, errors.New("区域不存在")
		}
		return models.Location{}, result.Error
	}

	// 检查库位编码是否已存在于该区域
	var count int64
	database.DB.Model(&models.Location{}).Where("zone_id = ? AND code = ?", req.ZoneID, req.Code).Count(&count)
	if count > 0 {
		return models.Location{}, errors.New("库位编码已存在于该区域")
	}

	// 创建库位
	location := models.Location{
		ZoneID:      req.ZoneID,
		Code:        req.Code,
		Name:        req.Name,
		LocationType: req.LocationType,
		Capacity:    req.Capacity,
		Used:        0, // 初始已使用容量为0
		Status:      1, // 默认启用
	}

	result = database.DB.Create(&location)
	if result.Error != nil {
		return models.Location{}, result.Error
	}

	// 加载关联的区域和仓库信息
	database.DB.Preload("Zone").Preload("Zone.Warehouse").First(&location, location.ID)

	return location, nil
}

// UpdateLocation 更新库位
func (s *WarehouseService) UpdateLocation(id uint, req models.LocationUpdateRequest) (models.Location, error) {
	var location models.Location

	// 查询库位
	result := database.DB.Preload("Zone").Preload("Zone.Warehouse").First(&location, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Location{}, errors.New("库位不存在")
		}
		return models.Location{}, result.Error
	}

	// 如果更新编码，检查编码是否已存在于该区域
	if req.Code != "" && req.Code != location.Code {
		var count int64
		database.DB.Model(&models.Location{}).Where("zone_id = ? AND code = ? AND id != ?", location.ZoneID, req.Code, id).Count(&count)
		if count > 0 {
			return models.Location{}, errors.New("库位编码已存在于该区域")
		}
		location.Code = req.Code
	}

	// 更新库位信息
	if req.Name != "" {
		location.Name = req.Name
	}
	if req.LocationType != "" {
		location.LocationType = req.LocationType
	}
	if req.Capacity != 0 {
		// 检查新容量是否小于已使用容量
		if req.Capacity < location.Used {
			return models.Location{}, errors.New("新容量不能小于已使用容量")
		}
		location.Capacity = req.Capacity
	}
	if req.Status != 0 {
		location.Status = req.Status
	}

	result = database.DB.Save(&location)
	if result.Error != nil {
		return models.Location{}, result.Error
	}

	return location, nil
}

// GetLocationByID 根据ID获取库位
func (s *WarehouseService) GetLocationByID(id uint) (models.Location, error) {
	var location models.Location

	result := database.DB.Preload("Zone").Preload("Zone.Warehouse").First(&location, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Location{}, errors.New("库位不存在")
		}
		return models.Location{}, result.Error
	}

	return location, nil
}

// GetLocationByCode 根据编码获取库位
func (s *WarehouseService) GetLocationByCode(zoneID uint, code string) (models.Location, error) {
	var location models.Location

	result := database.DB.Preload("Zone").Preload("Zone.Warehouse").Where("zone_id = ? AND code = ?", zoneID, code).First(&location)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Location{}, errors.New("库位不存在")
		}
		return models.Location{}, result.Error
	}

	return location, nil
}

// ListLocations 获取库位列表
func (s *WarehouseService) ListLocations(zoneID uint, page, pageSize int, query string, locationType string, status int) ([]models.Location, int64, error) {
	var locations []models.Location
	var total int64

	db := database.DB.Model(&models.Location{}).Preload("Zone").Preload("Zone.Warehouse")

	// 添加查询条件
	if zoneID != 0 {
		db = db.Where("zone_id = ?", zoneID)
	}

	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	if locationType != "" {
		db = db.Where("location_type = ?", locationType)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&locations)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return locations, total, nil
}

// DeleteLocation 删除库位
func (s *WarehouseService) DeleteLocation(id uint) error {
	var location models.Location

	// 查询库位
	result := database.DB.First(&location, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("库位不存在")
		}
		return result.Error
	}

	// 检查库位是否有关联的库存
	var count int64
	database.DB.Model(&models.Inventory{}).Where("location_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("库位存在关联库存，无法删除")
	}

	// 软删除库位
	result = database.DB.Delete(&location)
	if result.Error != nil {
		return result.Error
	}

	return nil
}