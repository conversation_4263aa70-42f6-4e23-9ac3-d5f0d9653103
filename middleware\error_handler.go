package middleware

import (
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// ErrorHandlerMiddleware 错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 使用defer+recover捕获panic
		defer func() {
			if err := recover(); err != nil {
				// 记录堆栈信息
				log.Printf("Panic: %v\n%s", err, debug.Stack())

				// 向客户端返回500错误
				c.AbortWithStatusJSON(http.StatusInternalServerError, ErrorResponse{
					Code:    http.StatusInternalServerError,
					Message: "服务器内部错误",
				})
			}
		}()

		c.Next()

		// 处理404错误
		if c.Writer.Status() == http.StatusNotFound {
			c.AbortWithStatusJSON(http.StatusNotFound, ErrorResponse{
				Code:    http.StatusNotFound,
				Message: "请求的资源不存在",
			})
			return
		}

		// 处理405错误
		if c.Writer.Status() == http.StatusMethodNotAllowed {
			c.AbortWithStatusJSON(http.StatusMethodNotAllowed, ErrorResponse{
				Code:    http.StatusMethodNotAllowed,
				Message: "请求方法不允许",
			})
			return
		}
	}
}

// CustomError 自定义错误结构
type CustomError struct {
	Code    int
	Message string
}

// Error 实现error接口
func (e *CustomError) Error() string {
	return e.Message
}

// NewError 创建新的自定义错误
func NewError(code int, message string) *CustomError {
	return &CustomError{
		Code:    code,
		Message: message,
	}
}

// HandleError 处理错误并返回适当的响应
func HandleError(c *gin.Context, err error) {
	// 检查是否为自定义错误
	if customErr, ok := err.(*CustomError); ok {
		c.JSON(customErr.Code, ErrorResponse{
			Code:    customErr.Code,
			Message: customErr.Message,
		})
		return
	}

	// 默认为内部服务器错误
	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Code:    http.StatusInternalServerError,
		Message: "服务器内部错误",
	})
}