package models

import (
	"time"

	"gorm.io/gorm"
)

// PurchaseOrder 采购订单
type PurchaseOrder struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	OrderNo      string         `json:"order_no" gorm:"size:50;uniqueIndex;not null"`
	SupplierID   uint           `json:"supplier_id" gorm:"index;not null"`
	Supplier     Supplier       `json:"supplier" gorm:"foreignKey:SupplierID"`
	OrderDate    time.Time      `json:"order_date"`
	ExpectedDate *time.Time     `json:"expected_date"` // 预计到货日期
	Status       int            `json:"status" gorm:"default:1"` // 1: 待审核, 2: 已审核, 3: 部分入库, 4: 已完成, 5: 已取消
	TotalAmount  float64        `json:"total_amount" gorm:"type:decimal(12,2);default:0"`
	Creator      string         `json:"creator" gorm:"size:50"`
	Approver     string         `json:"approver" gorm:"size:50"`
	ApproveTime  *time.Time     `json:"approve_time"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (PurchaseOrder) TableName() string {
	return "wms_purchase_order"
}

// PurchaseOrderItem 采购订单明细
type PurchaseOrderItem struct {
	ID            uint          `json:"id" gorm:"primaryKey"`
	OrderID       uint          `json:"order_id" gorm:"index;not null"`
	Order         PurchaseOrder `json:"order" gorm:"foreignKey:OrderID"`
	ProductID     uint          `json:"product_id" gorm:"index;not null"`
	Product       Product       `json:"product" gorm:"foreignKey:ProductID"`
	Quantity      int           `json:"quantity" gorm:"default:0"`
	ReceivedQty   int           `json:"received_qty" gorm:"default:0"`
	Price         float64       `json:"price" gorm:"type:decimal(10,2);default:0"`
	Amount        float64       `json:"amount" gorm:"type:decimal(12,2);default:0"`
	ProductionDate *time.Time    `json:"production_date"`
	ExpiryDate    *time.Time    `json:"expiry_date"`
	Remark        string        `json:"remark" gorm:"size:500"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at"`
}

// TableName 指定表名
func (PurchaseOrderItem) TableName() string {
	return "wms_purchase_order_item"
}

// ASN 到货通知单(Advance Shipping Notice)
type ASN struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ASNNo        string         `json:"asn_no" gorm:"size:50;uniqueIndex;not null"`
	SupplierID   uint           `json:"supplier_id" gorm:"index;not null"`
	Supplier     Supplier       `json:"supplier" gorm:"foreignKey:SupplierID"`
	OrderID      *uint          `json:"order_id" gorm:"index"` // 关联的采购订单ID，可能为空
	Order        *PurchaseOrder `json:"order" gorm:"foreignKey:OrderID"`
	ExpectedDate time.Time      `json:"expected_date"` // 预计到货日期
	Status       int            `json:"status" gorm:"default:1"` // 1: 待收货, 2: 部分收货, 3: 已完成
	Creator      string         `json:"creator" gorm:"size:50"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ASN) TableName() string {
	return "wms_asn"
}

// ASNItem 到货通知单明细
type ASNItem struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	ASNID         uint      `json:"asn_id" gorm:"index;not null"`
	ASN           ASN       `json:"asn" gorm:"foreignKey:ASNID"`
	OrderItemID   *uint     `json:"order_item_id" gorm:"index"` // 关联的采购订单明细ID，可能为空
	ProductID     uint      `json:"product_id" gorm:"index;not null"`
	Product       Product   `json:"product" gorm:"foreignKey:ProductID"`
	ExpectedQty   int       `json:"expected_qty" gorm:"default:0"`
	ReceivedQty   int       `json:"received_qty" gorm:"default:0"`
	ProductionDate *time.Time `json:"production_date"`
	ExpiryDate    *time.Time `json:"expiry_date"`
	Remark        string    `json:"remark" gorm:"size:500"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (ASNItem) TableName() string {
	return "wms_asn_item"
}

// ReceiptOrder 入库单
type ReceiptOrder struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ReceiptNo    string         `json:"receipt_no" gorm:"size:50;uniqueIndex;not null"`
	ReceiptType  string         `json:"receipt_type" gorm:"size:50"` // 入库类型：采购入库、退货入库、其他入库
	SupplierID   *uint          `json:"supplier_id" gorm:"index"`
	Supplier     *Supplier      `json:"supplier" gorm:"foreignKey:SupplierID"`
	OrderID      *uint          `json:"order_id" gorm:"index"` // 关联的采购订单ID，可能为空
	Order        *PurchaseOrder `json:"order" gorm:"foreignKey:OrderID"`
	ASNID        *uint          `json:"asn_id" gorm:"index"` // 关联的到货通知单ID，可能为空
	ASN          *ASN           `json:"asn" gorm:"foreignKey:ASNID"`
	WarehouseID  uint           `json:"warehouse_id" gorm:"index;not null"`
	Warehouse    Warehouse      `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 待质检, 2: 待上架, 3: 部分上架, 4: 已完成, 5: 已取消
	ReceiveTime  time.Time      `json:"receive_time"`
	Receiver     string         `json:"receiver" gorm:"size:50"`
	Inspector    string         `json:"inspector" gorm:"size:50"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ReceiptOrder) TableName() string {
	return "wms_receipt_order"
}

// ReceiptOrderItem 入库单明细
type ReceiptOrderItem struct {
	ID            uint         `json:"id" gorm:"primaryKey"`
	ReceiptID     uint         `json:"receipt_id" gorm:"index;not null"`
	Receipt       ReceiptOrder `json:"receipt" gorm:"foreignKey:ReceiptID"`
	ASNItemID     *uint        `json:"asn_item_id" gorm:"index"`
	ProductID     uint         `json:"product_id" gorm:"index;not null"`
	Product       Product      `json:"product" gorm:"foreignKey:ProductID"`
	BatchNo       string       `json:"batch_no" gorm:"size:50"`
	Quantity      int          `json:"quantity" gorm:"default:0"`
	InspectedQty  int          `json:"inspected_qty" gorm:"default:0"`
	QualifiedQty  int          `json:"qualified_qty" gorm:"default:0"`
	UnqualifiedQty int          `json:"unqualified_qty" gorm:"default:0"`
	PutawayQty    int          `json:"putaway_qty" gorm:"default:0"`
	ProductionDate *time.Time   `json:"production_date"`
	ExpiryDate    *time.Time   `json:"expiry_date"`
	Status        int          `json:"status" gorm:"default:1"` // 1: 待质检, 2: 已质检, 3: 待上架, 4: 已上架
	Remark        string       `json:"remark" gorm:"size:500"`
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`
}

// TableName 指定表名
func (ReceiptOrderItem) TableName() string {
	return "wms_receipt_order_item"
}

// PutawayTask 上架任务
type PutawayTask struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	TaskNo       string         `json:"task_no" gorm:"size:50;uniqueIndex;not null"`
	ReceiptID    uint           `json:"receipt_id" gorm:"index;not null"`
	Receipt      ReceiptOrder   `json:"receipt" gorm:"foreignKey:ReceiptID"`
	ReceiptItemID uint          `json:"receipt_item_id" gorm:"index;not null"`
	ReceiptItem  ReceiptOrderItem `json:"receipt_item" gorm:"foreignKey:ReceiptItemID"`
	ProductID    uint           `json:"product_id" gorm:"index;not null"`
	Product      Product        `json:"product" gorm:"foreignKey:ProductID"`
	BatchNo      string         `json:"batch_no" gorm:"size:50"`
	Quantity     int            `json:"quantity" gorm:"default:0"`
	LocationID   uint           `json:"location_id" gorm:"index;not null"`
	Location     Location       `json:"location" gorm:"foreignKey:LocationID"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 待上架, 2: 已上架
	Operator     string         `json:"operator" gorm:"size:50"`
	OperateTime  *time.Time     `json:"operate_time"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (PutawayTask) TableName() string {
	return "wms_putaway_task"
}