package main

import (
	"fmt"
	"log"
	"time"

	"wms-gin/config"
	"wms-gin/controllers"
	"wms-gin/internal/database"
	"wms-gin/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	config.LoadConfig()

	// 初始化数据库连接
	if err := database.InitDB(); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 设置运行模式
	if config.AppConfig.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin实例
	r := gin.New()

	// 使用日志和恢复中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// 添加请求ID中间件
	r.Use(middleware.RequestID())

	// 设置路由
	controllers.SetupRoutes(r)

	// 启动HTTP服务
	port := fmt.Sprintf(":%d", config.AppConfig.Port)
	log.Printf("服务启动在 http://localhost%s", port)
	if err := r.Run(port); err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
}