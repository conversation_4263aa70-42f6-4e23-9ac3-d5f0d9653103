package models

import (
	"time"

	"gorm.io/gorm"
)

// Warehouse 仓库模型
type Warehouse struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Code      string         `json:"code" gorm:"size:50;uniqueIndex;not null"`
	Name      string         `json:"name" gorm:"size:100;not null"`
	Address   string         `json:"address" gorm:"size:200"`
	Area      float64        `json:"area" gorm:"type:decimal(10,2)"` // 面积(平方米)
	Remark    string         `json:"remark" gorm:"size:500"`
	Status    int            `json:"status" gorm:"default:1"` // 1: 启用, 0: 禁用
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Warehouse) TableName() string {
	return "wms_warehouse"
}

// Zone 库区模型
type Zone struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	WarehouseID uint           `json:"warehouse_id" gorm:"index;not null"`
	Warehouse   Warehouse      `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	Code        string         `json:"code" gorm:"size:50;uniqueIndex;not null"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	ZoneType    string         `json:"zone_type" gorm:"size:50"` // 库区类型：普通区、冷藏区、危险品区等
	Remark      string         `json:"remark" gorm:"size:500"`
	Status      int            `json:"status" gorm:"default:1"` // 1: 启用, 0: 禁用
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Zone) TableName() string {
	return "wms_zone"
}

// Location 库位模型
type Location struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ZoneID    uint           `json:"zone_id" gorm:"index;not null"`
	Zone      Zone           `json:"zone" gorm:"foreignKey:ZoneID"`
	Code      string         `json:"code" gorm:"size:50;uniqueIndex;not null"`
	Shelf     string         `json:"shelf" gorm:"size:50"` // 货架号
	Row       int            `json:"row"`                   // 行
	Column    int            `json:"column"`                // 列
	Level     int            `json:"level"`                 // 层
	Capacity  float64        `json:"capacity" gorm:"type:decimal(10,2)"`  // 容量
	MaxWeight float64        `json:"max_weight" gorm:"type:decimal(10,2)"` // 最大承重(kg)
	Status    int            `json:"status" gorm:"default:1"`             // 1: 空闲, 2: 占用, 3: 锁定, 0: 禁用
	Remark    string         `json:"remark" gorm:"size:500"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Location) TableName() string {
	return "wms_location"
}

// WarehouseCreateRequest 仓库创建请求
type WarehouseCreateRequest struct {
	Code    string  `json:"code" binding:"required,max=50"`
	Name    string  `json:"name" binding:"required,max=100"`
	Address string  `json:"address" binding:"max=200"`
	Area    float64 `json:"area"`
	Remark  string  `json:"remark" binding:"max=500"`
}

// WarehouseUpdateRequest 仓库更新请求
type WarehouseUpdateRequest struct {
	Name    string  `json:"name" binding:"max=100"`
	Address string  `json:"address" binding:"max=200"`
	Area    float64 `json:"area"`
	Remark  string  `json:"remark" binding:"max=500"`
	Status  int     `json:"status" binding:"oneof=0 1"`
}

// ZoneCreateRequest 库区创建请求
type ZoneCreateRequest struct {
	WarehouseID uint   `json:"warehouse_id" binding:"required"`
	Code        string `json:"code" binding:"required,max=50"`
	Name        string `json:"name" binding:"required,max=100"`
	ZoneType    string `json:"zone_type" binding:"max=50"`
	Remark      string `json:"remark" binding:"max=500"`
}

// ZoneUpdateRequest 库区更新请求
type ZoneUpdateRequest struct {
	WarehouseID uint   `json:"warehouse_id"`
	Name        string `json:"name" binding:"max=100"`
	ZoneType    string `json:"zone_type" binding:"max=50"`
	Remark      string `json:"remark" binding:"max=500"`
	Status      int    `json:"status" binding:"oneof=0 1"`
}

// LocationCreateRequest 库位创建请求
type LocationCreateRequest struct {
	ZoneID    uint    `json:"zone_id" binding:"required"`
	Code      string  `json:"code" binding:"required,max=50"`
	Shelf     string  `json:"shelf" binding:"max=50"`
	Row       int     `json:"row"`
	Column    int     `json:"column"`
	Level     int     `json:"level"`
	Capacity  float64 `json:"capacity"`
	MaxWeight float64 `json:"max_weight"`
	Remark    string  `json:"remark" binding:"max=500"`
}

// LocationUpdateRequest 库位更新请求
type LocationUpdateRequest struct {
	ZoneID    uint    `json:"zone_id"`
	Shelf     string  `json:"shelf" binding:"max=50"`
	Row       int     `json:"row"`
	Column    int     `json:"column"`
	Level     int     `json:"level"`
	Capacity  float64 `json:"capacity"`
	MaxWeight float64 `json:"max_weight"`
	Status    int     `json:"status" binding:"oneof=0 1 2 3"`
	Remark    string  `json:"remark" binding:"max=500"`
}