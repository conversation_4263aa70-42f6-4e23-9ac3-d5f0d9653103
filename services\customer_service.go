package services

import (
	"errors"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// CustomerService 客户服务
type CustomerService struct{}

// CreateCustomer 创建客户
func (s *CustomerService) CreateCustomer(req models.CustomerCreateRequest) (models.Customer, error) {
	// 检查客户编码是否已存在
	var count int64
	database.DB.Model(&models.Customer{}).Where("code = ?", req.Code).Count(&count)
	if count > 0 {
		return models.Customer{}, errors.New("客户编码已存在")
	}

	// 创建客户
	customer := models.Customer{
		Code:        req.Code,
		Name:        req.Name,
		Contact:     req.Contact,
		Phone:       req.Phone,
		Email:       req.Email,
		Address:     req.Address,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	result := database.DB.Create(&customer)
	if result.Error != nil {
		return models.Customer{}, result.Error
	}

	return customer, nil
}

// UpdateCustomer 更新客户
func (s *CustomerService) UpdateCustomer(id uint, req models.CustomerUpdateRequest) (models.Customer, error) {
	var customer models.Customer

	// 查询客户
	result := database.DB.First(&customer, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Customer{}, errors.New("客户不存在")
		}
		return models.Customer{}, result.Error
	}

	// 如果更新编码，检查编码是否已存在
	if req.Code != "" && req.Code != customer.Code {
		var count int64
		database.DB.Model(&models.Customer{}).Where("code = ? AND id != ?", req.Code, id).Count(&count)
		if count > 0 {
			return models.Customer{}, errors.New("客户编码已存在")
		}
		customer.Code = req.Code
	}

	// 更新客户信息
	if req.Name != "" {
		customer.Name = req.Name
	}
	if req.Contact != "" {
		customer.Contact = req.Contact
	}
	if req.Phone != "" {
		customer.Phone = req.Phone
	}
	if req.Email != "" {
		customer.Email = req.Email
	}
	if req.Address != "" {
		customer.Address = req.Address
	}
	if req.Description != "" {
		customer.Description = req.Description
	}
	if req.Status != 0 {
		customer.Status = req.Status
	}

	result = database.DB.Save(&customer)
	if result.Error != nil {
		return models.Customer{}, result.Error
	}

	return customer, nil
}

// GetCustomerByID 根据ID获取客户
func (s *CustomerService) GetCustomerByID(id uint) (models.Customer, error) {
	var customer models.Customer

	result := database.DB.First(&customer, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Customer{}, errors.New("客户不存在")
		}
		return models.Customer{}, result.Error
	}

	return customer, nil
}

// GetCustomerByCode 根据编码获取客户
func (s *CustomerService) GetCustomerByCode(code string) (models.Customer, error) {
	var customer models.Customer

	result := database.DB.Where("code = ?", code).First(&customer)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Customer{}, errors.New("客户不存在")
		}
		return models.Customer{}, result.Error
	}

	return customer, nil
}

// ListCustomers 获取客户列表
func (s *CustomerService) ListCustomers(page, pageSize int, query string, status int) ([]models.Customer, int64, error) {
	var customers []models.Customer
	var total int64

	db := database.DB.Model(&models.Customer{})

	// 添加查询条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR contact LIKE ? OR phone LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&customers)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return customers, total, nil
}

// DeleteCustomer 删除客户
func (s *CustomerService) DeleteCustomer(id uint) error {
	var customer models.Customer

	// 查询客户
	result := database.DB.First(&customer, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("客户不存在")
		}
		return result.Error
	}

	// 检查客户是否有关联的销售订单
	var count int64
	database.DB.Model(&models.SalesOrder{}).Where("customer_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("客户存在关联销售订单，无法删除")
	}

	// 软删除客户
	result = database.DB.Delete(&customer)
	if result.Error != nil {
		return result.Error
	}

	return nil
}