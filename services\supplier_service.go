package services

import (
	"errors"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// SupplierService 供应商服务
type SupplierService struct{}

// CreateSupplier 创建供应商
func (s *SupplierService) CreateSupplier(req models.SupplierCreateRequest) (models.Supplier, error) {
	// 检查供应商编码是否已存在
	var count int64
	database.DB.Model(&models.Supplier{}).Where("code = ?", req.Code).Count(&count)
	if count > 0 {
		return models.Supplier{}, errors.New("供应商编码已存在")
	}

	// 创建供应商
	supplier := models.Supplier{
		Code:        req.Code,
		Name:        req.Name,
		Contact:     req.Contact,
		Phone:       req.Phone,
		Email:       req.Email,
		Address:     req.Address,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	result := database.DB.Create(&supplier)
	if result.Error != nil {
		return models.Supplier{}, result.Error
	}

	return supplier, nil
}

// UpdateSupplier 更新供应商
func (s *SupplierService) UpdateSupplier(id uint, req models.SupplierUpdateRequest) (models.Supplier, error) {
	var supplier models.Supplier

	// 查询供应商
	result := database.DB.First(&supplier, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Supplier{}, errors.New("供应商不存在")
		}
		return models.Supplier{}, result.Error
	}

	// 如果更新编码，检查编码是否已存在
	if req.Code != "" && req.Code != supplier.Code {
		var count int64
		database.DB.Model(&models.Supplier{}).Where("code = ? AND id != ?", req.Code, id).Count(&count)
		if count > 0 {
			return models.Supplier{}, errors.New("供应商编码已存在")
		}
		supplier.Code = req.Code
	}

	// 更新供应商信息
	if req.Name != "" {
		supplier.Name = req.Name
	}
	if req.Contact != "" {
		supplier.Contact = req.Contact
	}
	if req.Phone != "" {
		supplier.Phone = req.Phone
	}
	if req.Email != "" {
		supplier.Email = req.Email
	}
	if req.Address != "" {
		supplier.Address = req.Address
	}
	if req.Description != "" {
		supplier.Description = req.Description
	}
	if req.Status != 0 {
		supplier.Status = req.Status
	}

	result = database.DB.Save(&supplier)
	if result.Error != nil {
		return models.Supplier{}, result.Error
	}

	return supplier, nil
}

// GetSupplierByID 根据ID获取供应商
func (s *SupplierService) GetSupplierByID(id uint) (models.Supplier, error) {
	var supplier models.Supplier

	result := database.DB.First(&supplier, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Supplier{}, errors.New("供应商不存在")
		}
		return models.Supplier{}, result.Error
	}

	return supplier, nil
}

// GetSupplierByCode 根据编码获取供应商
func (s *SupplierService) GetSupplierByCode(code string) (models.Supplier, error) {
	var supplier models.Supplier

	result := database.DB.Where("code = ?", code).First(&supplier)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Supplier{}, errors.New("供应商不存在")
		}
		return models.Supplier{}, result.Error
	}

	return supplier, nil
}

// ListSuppliers 获取供应商列表
func (s *SupplierService) ListSuppliers(page, pageSize int, query string, status int) ([]models.Supplier, int64, error) {
	var suppliers []models.Supplier
	var total int64

	db := database.DB.Model(&models.Supplier{})

	// 添加查询条件
	if query != "" {
		db = db.Where("code LIKE ? OR name LIKE ? OR contact LIKE ? OR phone LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&suppliers)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return suppliers, total, nil
}

// DeleteSupplier 删除供应商
func (s *SupplierService) DeleteSupplier(id uint) error {
	var supplier models.Supplier

	// 查询供应商
	result := database.DB.First(&supplier, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("供应商不存在")
		}
		return result.Error
	}

	// 检查供应商是否有关联的商品
	var count int64
	database.DB.Model(&models.Product{}).Where("supplier_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("供应商存在关联商品，无法删除")
	}

	// 检查供应商是否有关联的采购订单
	database.DB.Model(&models.PurchaseOrder{}).Where("supplier_id = ?", id).Count(&count)
	if count > 0 {
		return errors.New("供应商存在关联采购订单，无法删除")
	}

	// 软删除供应商
	result = database.DB.Delete(&supplier)
	if result.Error != nil {
		return result.Error
	}

	return nil
}