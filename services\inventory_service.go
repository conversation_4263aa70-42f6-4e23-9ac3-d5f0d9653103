package services

import (
	"errors"
	"time"

	"wms-gin/internal/database"
	"wms-gin/models"

	"gorm.io/gorm"
)

// InventoryService 库存服务
type InventoryService struct{}

// GetInventory 获取指定商品和库位的库存
func (s *InventoryService) GetInventory(productID, locationID uint) (models.Inventory, error) {
	var inventory models.Inventory

	result := database.DB.Preload("Product").Preload("Location").Preload("Location.Zone").Preload("Location.Zone.Warehouse").Where("product_id = ? AND location_id = ?", productID, locationID).First(&inventory)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Inventory{}, errors.New("库存记录不存在")
		}
		return models.Inventory{}, result.Error
	}

	return inventory, nil
}

// GetInventoryByID 根据ID获取库存
func (s *InventoryService) GetInventoryByID(id uint) (models.Inventory, error) {
	var inventory models.Inventory

	result := database.DB.Preload("Product").Preload("Location").Preload("Location.Zone").Preload("Location.Zone.Warehouse").First(&inventory, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.Inventory{}, errors.New("库存记录不存在")
		}
		return models.Inventory{}, result.Error
	}

	return inventory, nil
}

// ListInventory 获取库存列表
func (s *InventoryService) ListInventory(page, pageSize int, warehouseID, zoneID, locationID, productID uint, query string) ([]models.Inventory, int64, error) {
	var inventories []models.Inventory
	var total int64

	db := database.DB.Model(&models.Inventory{}).Preload("Product").Preload("Location").Preload("Location.Zone").Preload("Location.Zone.Warehouse")

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Joins("JOIN locations ON inventories.location_id = locations.id").Joins("JOIN zones ON locations.zone_id = zones.id").Where("zones.warehouse_id = ?", warehouseID)
	}

	if zoneID != 0 {
		db = db.Joins("JOIN locations ON inventories.location_id = locations.id").Where("locations.zone_id = ?", zoneID)
	}

	if locationID != 0 {
		db = db.Where("location_id = ?", locationID)
	}

	if productID != 0 {
		db = db.Where("product_id = ?", productID)
	}

	if query != "" {
		db = db.Joins("JOIN products ON inventories.product_id = products.id").Where("products.sku LIKE ? OR products.name LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&inventories)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return inventories, total, nil
}

// UpdateInventory 更新库存
func (s *InventoryService) UpdateInventory(productID, locationID uint, quantity int, operationType string, remark string) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询商品
	var product models.Product
	result := tx.First(&product, productID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("商品不存在")
		}
		return result.Error
	}

	// 查询库位
	var location models.Location
	result = tx.First(&location, locationID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("库位不存在")
		}
		return result.Error
	}

	// 查询库存记录
	var inventory models.Inventory
	result = tx.Where("product_id = ? AND location_id = ?", productID, locationID).First(&inventory)

	// 如果库存记录不存在，创建新记录
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		// 如果是减少库存操作，但库存记录不存在，则返回错误
		if operationType == "出库" && quantity < 0 {
			tx.Rollback()
			return errors.New("库存不足")
		}

		// 创建新的库存记录
		inventory = models.Inventory{
			ProductID:  productID,
			LocationID: locationID,
			Quantity:   quantity,
		}

		result = tx.Create(&inventory)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
	} else if result.Error != nil {
		tx.Rollback()
		return result.Error
	} else {
		// 更新库存数量
		newQuantity := inventory.Quantity + quantity

		// 如果是减少库存操作，检查库存是否足够
		if newQuantity < 0 {
			tx.Rollback()
			return errors.New("库存不足")
		}

		// 更新库存记录
		inventory.Quantity = newQuantity
		result = tx.Save(&inventory)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
	}

	// 创建库存日志
	inventoryLog := models.InventoryLog{
		ProductID:     productID,
		LocationID:    locationID,
		Quantity:      quantity,
		OperationType: operationType,
		Remark:        remark,
	}

	result = tx.Create(&inventoryLog)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}

	// 更新库位已使用容量
	if operationType == "入库" {
		location.Used += quantity
	} else if operationType == "出库" {
		location.Used += quantity // quantity为负数，所以是减少
	}

	// 检查库位容量是否超出
	if location.Used > location.Capacity {
		tx.Rollback()
		return errors.New("库位容量不足")
	}

	result = tx.Save(&location)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 提交事务
	return tx.Commit().Error
}

// MoveInventory 移动库存
func (s *InventoryService) MoveInventory(productID, fromLocationID, toLocationID uint, quantity int, remark string) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查源库位和目标库位是否相同
	if fromLocationID == toLocationID {
		tx.Rollback()
		return errors.New("源库位和目标库位不能相同")
	}

	// 查询商品
	var product models.Product
	result := tx.First(&product, productID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("商品不存在")
		}
		return result.Error
	}

	// 查询源库位
	var fromLocation models.Location
	result = tx.First(&fromLocation, fromLocationID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("源库位不存在")
		}
		return result.Error
	}

	// 查询目标库位
	var toLocation models.Location
	result = tx.First(&toLocation, toLocationID)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("目标库位不存在")
		}
		return result.Error
	}

	// 查询源库位的库存记录
	var fromInventory models.Inventory
	result = tx.Where("product_id = ? AND location_id = ?", productID, fromLocationID).First(&fromInventory)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("源库位没有该商品的库存")
		}
		return result.Error
	}

	// 检查源库位的库存是否足够
	if fromInventory.Quantity < quantity {
		tx.Rollback()
		return errors.New("源库位库存不足")
	}

	// 查询目标库位的库存记录
	var toInventory models.Inventory
	result = tx.Where("product_id = ? AND location_id = ?", productID, toLocationID).First(&toInventory)

	// 如果目标库位没有该商品的库存记录，创建新记录
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		toInventory = models.Inventory{
			ProductID:  productID,
			LocationID: toLocationID,
			Quantity:   0,
		}

		result = tx.Create(&toInventory)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
	} else if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 更新源库位的库存
	fromInventory.Quantity -= quantity
	result = tx.Save(&fromInventory)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 更新目标库位的库存
	toInventory.Quantity += quantity
	result = tx.Save(&toInventory)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 更新源库位和目标库位的已使用容量
	fromLocation.Used -= quantity
	result = tx.Save(&fromLocation)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	toLocation.Used += quantity
	// 检查目标库位容量是否超出
	if toLocation.Used > toLocation.Capacity {
		tx.Rollback()
		return errors.New("目标库位容量不足")
	}

	result = tx.Save(&toLocation)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 创建库存移动记录
	inventoryMove := models.InventoryMove{
		ProductID:      productID,
		FromLocationID: fromLocationID,
		ToLocationID:   toLocationID,
		Quantity:       quantity,
		Remark:         remark,
	}

	result = tx.Create(&inventoryMove)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 创建源库位的库存日志
	fromInventoryLog := models.InventoryLog{
		ProductID:     productID,
		LocationID:    fromLocationID,
		Quantity:      -quantity,
		OperationType: "移出",
		Remark:        remark,
	}

	result = tx.Create(&fromInventoryLog)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 创建目标库位的库存日志
	toInventoryLog := models.InventoryLog{
		ProductID:     productID,
		LocationID:    toLocationID,
		Quantity:      quantity,
		OperationType: "移入",
		Remark:        remark,
	}

	result = tx.Create(&toInventoryLog)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 提交事务
	return tx.Commit().Error
}

// CreateInventoryCheck 创建库存盘点单
func (s *InventoryService) CreateInventoryCheck(req models.InventoryCheckCreateRequest) (models.InventoryCheck, error) {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建盘点单
	inventoryCheck := models.InventoryCheck{
		WarehouseID: req.WarehouseID,
		ZoneID:      req.ZoneID,
		CheckType:   req.CheckType,
		Status:      1, // 待盘点
		Remark:      req.Remark,
	}

	result := tx.Create(&inventoryCheck)
	if result.Error != nil {
		tx.Rollback()
		return models.InventoryCheck{}, result.Error
	}

	// 根据盘点类型和范围查询需要盘点的库存
	var inventories []models.Inventory
	db := tx.Preload("Product").Preload("Location").Preload("Location.Zone").Preload("Location.Zone.Warehouse")

	if req.CheckType == "全盘" {
		// 全盘：盘点指定仓库或区域的所有库存
		if req.WarehouseID != 0 && req.ZoneID == 0 {
			// 盘点整个仓库
			db = db.Joins("JOIN locations ON inventories.location_id = locations.id").Joins("JOIN zones ON locations.zone_id = zones.id").Where("zones.warehouse_id = ?", req.WarehouseID)
		} else if req.ZoneID != 0 {
			// 盘点指定区域
			db = db.Joins("JOIN locations ON inventories.location_id = locations.id").Where("locations.zone_id = ?", req.ZoneID)
		}
	} else if req.CheckType == "抽盘" {
		// 抽盘：盘点指定的商品或库位
		if len(req.ProductIDs) > 0 {
			db = db.Where("product_id IN ?", req.ProductIDs)
		}

		if len(req.LocationIDs) > 0 {
			db = db.Where("location_id IN ?", req.LocationIDs)
		}
	}

	result = db.Find(&inventories)
	if result.Error != nil {
		tx.Rollback()
		return models.InventoryCheck{}, result.Error
	}

	// 创建盘点明细
	for _, inventory := range inventories {
		checkItem := models.InventoryCheckItem{
			CheckID:      inventoryCheck.ID,
			ProductID:    inventory.ProductID,
			LocationID:   inventory.LocationID,
			BookQuantity: inventory.Quantity,
			RealQuantity: 0, // 初始实际数量为0，等待盘点
			Status:       1, // 待盘点
		}

		result = tx.Create(&checkItem)
		if result.Error != nil {
			tx.Rollback()
			return models.InventoryCheck{}, result.Error
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return models.InventoryCheck{}, err
	}

	// 加载关联信息
	database.DB.Preload("Items").Preload("Items.Product").Preload("Items.Location").First(&inventoryCheck, inventoryCheck.ID)

	return inventoryCheck, nil
}

// UpdateInventoryCheckItem 更新盘点明细
func (s *InventoryService) UpdateInventoryCheckItem(id uint, realQuantity int) (models.InventoryCheckItem, error) {
	var checkItem models.InventoryCheckItem

	// 查询盘点明细
	result := database.DB.Preload("Check").Preload("Product").Preload("Location").First(&checkItem, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.InventoryCheckItem{}, errors.New("盘点明细不存在")
		}
		return models.InventoryCheckItem{}, result.Error
	}

	// 检查盘点单状态
	if checkItem.Check.Status != 1 {
		return models.InventoryCheckItem{}, errors.New("盘点单状态不允许更新")
	}

	// 更新实际数量和状态
	checkItem.RealQuantity = realQuantity
	checkItem.Status = 2 // 已盘点

	// 计算差异
	checkItem.DiffQuantity = realQuantity - checkItem.BookQuantity

	result = database.DB.Save(&checkItem)
	if result.Error != nil {
		return models.InventoryCheckItem{}, result.Error
	}

	return checkItem, nil
}

// CompleteInventoryCheck 完成盘点
func (s *InventoryService) CompleteInventoryCheck(id uint, adjustInventory bool) error {
	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询盘点单
	var check models.InventoryCheck
	result := tx.Preload("Items").First(&check, id)
	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("盘点单不存在")
		}
		return result.Error
	}

	// 检查盘点单状态
	if check.Status != 1 {
		tx.Rollback()
		return errors.New("盘点单状态不允许完成")
	}

	// 检查是否所有明细都已盘点
	for _, item := range check.Items {
		if item.Status == 1 {
			tx.Rollback()
			return errors.New("存在未盘点的明细，无法完成盘点")
		}
	}

	// 如果需要调整库存
	if adjustInventory {
		for _, item := range check.Items {
			// 只处理有差异的明细
			if item.DiffQuantity != 0 {
				// 查询库存
				var inventory models.Inventory
				result = tx.Where("product_id = ? AND location_id = ?", item.ProductID, item.LocationID).First(&inventory)
				if result.Error != nil {
					tx.Rollback()
					return result.Error
				}

				// 更新库存数量
				inventory.Quantity = item.RealQuantity
				result = tx.Save(&inventory)
				if result.Error != nil {
					tx.Rollback()
					return result.Error
				}

				// 创建库存日志
				inventoryLog := models.InventoryLog{
					ProductID:     item.ProductID,
					LocationID:    item.LocationID,
					Quantity:      item.DiffQuantity,
					OperationType: "盘点调整",
					Remark:        "盘点单号：" + check.ID,
				}

				result = tx.Create(&inventoryLog)
				if result.Error != nil {
					tx.Rollback()
					return result.Error
				}

				// 更新库位已使用容量
				var location models.Location
				result = tx.First(&location, item.LocationID)
				if result.Error != nil {
					tx.Rollback()
					return result.Error
				}

				location.Used += item.DiffQuantity
				result = tx.Save(&location)
				if result.Error != nil {
					tx.Rollback()
					return result.Error
				}
			}
		}
	}

	// 更新盘点单状态
	check.Status = 2 // 已完成
	check.CompletedAt = time.Now()

	result = tx.Save(&check)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	// 提交事务
	return tx.Commit().Error
}

// GetInventoryCheckByID 根据ID获取盘点单
func (s *InventoryService) GetInventoryCheckByID(id uint) (models.InventoryCheck, error) {
	var check models.InventoryCheck

	result := database.DB.Preload("Items").Preload("Items.Product").Preload("Items.Location").Preload("Items.Location.Zone").Preload("Items.Location.Zone.Warehouse").First(&check, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return models.InventoryCheck{}, errors.New("盘点单不存在")
		}
		return models.InventoryCheck{}, result.Error
	}

	return check, nil
}

// ListInventoryChecks 获取盘点单列表
func (s *InventoryService) ListInventoryChecks(page, pageSize int, warehouseID, zoneID uint, status int) ([]models.InventoryCheck, int64, error) {
	var checks []models.InventoryCheck
	var total int64

	db := database.DB.Model(&models.InventoryCheck{})

	// 添加查询条件
	if warehouseID != 0 {
		db = db.Where("warehouse_id = ?", warehouseID)
	}

	if zoneID != 0 {
		db = db.Where("zone_id = ?", zoneID)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&checks)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return checks, total, nil
}

// GetInventoryLogs 获取库存日志
func (s *InventoryService) GetInventoryLogs(page, pageSize int, productID, locationID uint, operationType string, startTime, endTime *time.Time) ([]models.InventoryLog, int64, error) {
	var logs []models.InventoryLog
	var total int64

	db := database.DB.Model(&models.InventoryLog{}).Preload("Product").Preload("Location").Preload("Location.Zone").Preload("Location.Zone.Warehouse")

	// 添加查询条件
	if productID != 0 {
		db = db.Where("product_id = ?", productID)
	}

	if locationID != 0 {
		db = db.Where("location_id = ?", locationID)
	}

	if operationType != "" {
		db = db.Where("operation_type = ?", operationType)
	}

	if startTime != nil {
		db = db.Where("created_at >= ?", startTime)
	}

	if endTime != nil {
		db = db.Where("created_at <= ?", endTime)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&logs)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return logs, total, nil
}

// GetInventoryMoves 获取库存移动记录
func (s *InventoryService) GetInventoryMoves(page, pageSize int, productID, fromLocationID, toLocationID uint, startTime, endTime *time.Time) ([]models.InventoryMove, int64, error) {
	var moves []models.InventoryMove
	var total int64

	db := database.DB.Model(&models.InventoryMove{}).Preload("Product").Preload("FromLocation").Preload("FromLocation.Zone").Preload("FromLocation.Zone.Warehouse").Preload("ToLocation").Preload("ToLocation.Zone").Preload("ToLocation.Zone.Warehouse")

	// 添加查询条件
	if productID != 0 {
		db = db.Where("product_id = ?", productID)
	}

	if fromLocationID != 0 {
		db = db.Where("from_location_id = ?", fromLocationID)
	}

	if toLocationID != 0 {
		db = db.Where("to_location_id = ?", toLocationID)
	}

	if startTime != nil {
		db = db.Where("created_at >= ?", startTime)
	}

	if endTime != nil {
		db = db.Where("created_at <= ?", endTime)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&moves)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return moves, total, nil
}

// CheckInventoryAlerts 检查库存预警
func (s *InventoryService) CheckInventoryAlerts() ([]models.InventoryAlert, error) {
	var alerts []models.InventoryAlert

	// 查询所有商品
	var products []models.Product
	result := database.DB.Find(&products)
	if result.Error != nil {
		return nil, result.Error
	}

	// 对每个商品检查库存预警
	for _, product := range products {
		// 查询该商品的总库存
		var totalQuantity int64
		database.DB.Model(&models.Inventory{}).Where("product_id = ?", product.ID).Select("COALESCE(SUM(quantity), 0)").Scan(&totalQuantity)

		// 检查是否低于最小库存或超过最大库存
		var alertType string
		var alertThreshold int

		if int(totalQuantity) < product.MinStock {
			alertType = "低库存"
			alertThreshold = product.MinStock
		} else if product.MaxStock > 0 && int(totalQuantity) > product.MaxStock {
			alertType = "超库存"
			alertThreshold = product.MaxStock
		} else {
			// 库存正常，继续下一个商品
			continue
		}

		// 检查是否已存在未处理的相同类型预警
		var count int64
		database.DB.Model(&models.InventoryAlert{}).Where("product_id = ? AND alert_type = ? AND status = 1", product.ID, alertType).Count(&count)
		if count > 0 {
			// 已存在未处理的预警，继续下一个商品
			continue
		}

		// 创建库存预警
		alert := models.InventoryAlert{
			ProductID:      product.ID,
			CurrentQuantity: int(totalQuantity),
			AlertType:      alertType,
			AlertThreshold: alertThreshold,
			Status:         1, // 未处理
		}

		result = database.DB.Create(&alert)
		if result.Error != nil {
			return nil, result.Error
		}

		// 加载关联的商品信息
		database.DB.Preload("Product").First(&alert, alert.ID)

		alerts = append(alerts, alert)
	}

	return alerts, nil
}

// GetInventoryAlerts 获取库存预警
func (s *InventoryService) GetInventoryAlerts(page, pageSize int, alertType string, status int) ([]models.InventoryAlert, int64, error) {
	var alerts []models.InventoryAlert
	var total int64

	db := database.DB.Model(&models.InventoryAlert{}).Preload("Product")

	// 添加查询条件
	if alertType != "" {
		db = db.Where("alert_type = ?", alertType)
	}

	if status != 0 {
		db = db.Where("status = ?", status)
	}

	// 获取总数
	db.Count(&total)

	// 分页查询
	result := db.Order("created_at DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&alerts)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return alerts, total, nil
}

// ProcessInventoryAlert 处理库存预警
func (s *InventoryService) ProcessInventoryAlert(id uint, remark string) error {
	var alert models.InventoryAlert

	// 查询预警
	result := database.DB.First(&alert, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("库存预警不存在")
		}
		return result.Error
	}

	// 更新预警状态
	alert.Status = 2 // 已处理
	alert.ProcessedAt = time.Now()
	alert.ProcessRemark = remark

	result = database.DB.Save(&alert)
	if result.Error != nil {
		return result.Error
	}

	return nil
}