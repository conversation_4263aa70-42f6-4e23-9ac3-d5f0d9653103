package controllers

import (
	"net/http"
	"strconv"
	"time"

	"wms-gin/middleware"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// ReportController 报表控制器
type ReportController struct {
	inventoryService services.InventoryService
	inboundService   services.InboundService
	outboundService  services.OutboundService
}

// NewReportController 创建报表控制器
func NewReportController() *ReportController {
	return &ReportController{}
}

// GetInventoryReport 获取库存报表
func (ctrl *ReportController) GetInventoryReport(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.<PERSON>("warehouseID", "0"))
	zoneID, _ := strconv.Atoi(c.<PERSON>("zoneID", "0"))
	productID, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON><PERSON>("productID", "0"))
	sku := c.Query("sku")

	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>faultQuery("pageSize", "10"))

	// 调用服务获取库存报表
	report, total, err := ctrl.inventoryService.GetInventoryReport(
		page,
		pageSize,
		uint(warehouseID),
		uint(zoneID),
		uint(productID),
		sku,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetInventoryMovementReport 获取库存移动报表
func (ctrl *ReportController) GetInventoryMovementReport(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	sku := c.Query("sku")
	operationType, _ := strconv.Atoi(c.DefaultQuery("operationType", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取库存移动报表
	report, total, err := ctrl.inventoryService.GetInventoryMovementReport(
		page,
		pageSize,
		uint(warehouseID),
		uint(productID),
		sku,
		operationType,
		startDate,
		endDate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetInboundReport 获取入库报表
func (ctrl *ReportController) GetInboundReport(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	supplierID, _ := strconv.Atoi(c.DefaultQuery("supplierID", "0"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	sku := c.Query("sku")
	reportType := c.DefaultQuery("reportType", "daily") // daily, weekly, monthly

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取入库报表
	report, total, err := ctrl.inboundService.GetInboundReport(
		page,
		pageSize,
		uint(warehouseID),
		uint(supplierID),
		uint(productID),
		sku,
		reportType,
		startDate,
		endDate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetOutboundReport 获取出库报表
func (ctrl *ReportController) GetOutboundReport(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	customerID, _ := strconv.Atoi(c.DefaultQuery("customerID", "0"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	sku := c.Query("sku")
	reportType := c.DefaultQuery("reportType", "daily") // daily, weekly, monthly

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取出库报表
	report, total, err := ctrl.outboundService.GetOutboundReport(
		page,
		pageSize,
		uint(warehouseID),
		uint(customerID),
		uint(productID),
		sku,
		reportType,
		startDate,
		endDate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetInventoryCheckReport 获取库存盘点报表
func (ctrl *ReportController) GetInventoryCheckReport(c *gin.Context) {
	// 获取查询参数
	warehouseID, _ := strconv.Atoi(c.DefaultQuery("warehouseID", "0"))
	zoneID, _ := strconv.Atoi(c.DefaultQuery("zoneID", "0"))
	productID, _ := strconv.Atoi(c.DefaultQuery("productID", "0"))
	sku := c.Query("sku")
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取库存盘点报表
	report, total, err := ctrl.inventoryService.GetInventoryCheckReport(
		page,
		pageSize,
		uint(warehouseID),
		uint(zoneID),
		uint(productID),
		sku,
		status,
		startDate,
		endDate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// GetOperationLogReport 获取操作日志报表
func (ctrl *ReportController) GetOperationLogReport(c *gin.Context) {
	// 获取查询参数
	userID, _ := strconv.Atoi(c.DefaultQuery("userID", "0"))
	module := c.Query("module")
	action := c.Query("action")

	// 解析日期参数
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		date, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = &date
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		date, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			endDate = &date
		}
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 调用服务获取操作日志报表
	// 注意：这里假设操作日志服务在其他服务中已经实现
	// 实际项目中需要根据具体实现调整
	report, total, err := services.GetOperationLogReport(
		page,
		pageSize,
		uint(userID),
		module,
		action,
		startDate,
		endDate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  report,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// RegisterRoutes 注册路由
func (ctrl *ReportController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/report", middleware.JWTAuth())
		{
			// 库存报表
			auth.GET("/inventory", ctrl.GetInventoryReport)
			auth.GET("/inventory/movement", ctrl.GetInventoryMovementReport)
			auth.GET("/inventory/check", ctrl.GetInventoryCheckReport)

			// 入库报表
			auth.GET("/inbound", ctrl.GetInboundReport)

			// 出库报表
			auth.GET("/outbound", ctrl.GetOutboundReport)

			// 操作日志报表
			auth.GET("/operation-log", ctrl.GetOperationLogReport)
		}
	}
}