package models

import (
	"time"

	"gorm.io/gorm"
)

// Inventory 库存模型
type Inventory struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	ProductID  uint           `json:"product_id" gorm:"index;not null"`
	Product    Product        `json:"product" gorm:"foreignKey:ProductID"`
	LocationID uint           `json:"location_id" gorm:"index;not null"`
	Location   Location       `json:"location" gorm:"foreignKey:LocationID"`
	BatchID    *uint          `json:"batch_id" gorm:"index"`
	Batch      *ProductBatch  `json:"batch" gorm:"foreignKey:BatchID"`
	Quantity   int            `json:"quantity" gorm:"default:0"`
	Status     int            `json:"status" gorm:"default:1"` // 1: 正常, 2: 锁定
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Inventory) TableName() string {
	return "wms_inventory"
}

// InventoryLog 库存日志
type InventoryLog struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ProductID   uint           `json:"product_id" gorm:"index;not null"`
	Product     Product        `json:"product" gorm:"foreignKey:ProductID"`
	LocationID  uint           `json:"location_id" gorm:"index"`
	Location    Location       `json:"location" gorm:"foreignKey:LocationID"`
	BatchID     *uint          `json:"batch_id" gorm:"index"`
	Batch       *ProductBatch  `json:"batch" gorm:"foreignKey:BatchID"`
	SerialID    *uint          `json:"serial_id" gorm:"index"`
	Serial      *ProductSerial `json:"serial" gorm:"foreignKey:SerialID"`
	Quantity    int            `json:"quantity"`                     // 变动数量，正数为增加，负数为减少
	BeforeQty   int            `json:"before_qty"`                   // 变动前数量
	AfterQty    int            `json:"after_qty"`                    // 变动后数量
	Type        string         `json:"type" gorm:"size:50;not null"` // 类型：入库、出库、调整、盘点等
	ReferenceID *uint          `json:"reference_id"`                 // 关联单据ID
	ReferenceNo string         `json:"reference_no" gorm:"size:50"` // 关联单据编号
	Operator    string         `json:"operator" gorm:"size:50"`     // 操作人
	Remark      string         `json:"remark" gorm:"size:500"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// TableName 指定表名
func (InventoryLog) TableName() string {
	return "wms_inventory_log"
}

// InventoryCheck 库存盘点
type InventoryCheck struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	CheckNo      string         `json:"check_no" gorm:"size:50;uniqueIndex;not null"`
	WarehouseID  uint           `json:"warehouse_id" gorm:"index"`
	Warehouse    Warehouse      `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	ZoneID       *uint          `json:"zone_id" gorm:"index"`
	Zone         *Zone          `json:"zone" gorm:"foreignKey:ZoneID"`
	CheckType    string         `json:"check_type" gorm:"size:50"` // 盘点类型：全盘、抽盘、动态盘点
	Status       int            `json:"status" gorm:"default:1"`   // 1: 待盘点, 2: 盘点中, 3: 已完成
	StartTime    *time.Time     `json:"start_time"`
	EndTime      *time.Time     `json:"end_time"`
	Creator      string         `json:"creator" gorm:"size:50"`
	Checker      string         `json:"checker" gorm:"size:50"`
	Remark       string         `json:"remark" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (InventoryCheck) TableName() string {
	return "wms_inventory_check"
}

// InventoryCheckItem 库存盘点明细
type InventoryCheckItem struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	CheckID        uint           `json:"check_id" gorm:"index;not null"`
	Check          InventoryCheck `json:"check" gorm:"foreignKey:CheckID"`
	ProductID      uint           `json:"product_id" gorm:"index;not null"`
	Product        Product        `json:"product" gorm:"foreignKey:ProductID"`
	LocationID     uint           `json:"location_id" gorm:"index;not null"`
	Location       Location       `json:"location" gorm:"foreignKey:LocationID"`
	BatchID        *uint          `json:"batch_id" gorm:"index"`
	Batch          *ProductBatch  `json:"batch" gorm:"foreignKey:BatchID"`
	SystemQuantity int            `json:"system_quantity"` // 系统数量
	ActualQuantity int            `json:"actual_quantity"` // 实际数量
	Difference     int            `json:"difference"`      // 差异数量
	Status         int            `json:"status" gorm:"default:1"` // 1: 待盘点, 2: 已盘点, 3: 已调整
	Remark         string         `json:"remark" gorm:"size:500"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
}

// TableName 指定表名
func (InventoryCheckItem) TableName() string {
	return "wms_inventory_check_item"
}

// InventoryMove 库存移动
type InventoryMove struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	MoveNo           string         `json:"move_no" gorm:"size:50;uniqueIndex;not null"`
	ProductID        uint           `json:"product_id" gorm:"index;not null"`
	Product          Product        `json:"product" gorm:"foreignKey:ProductID"`
	BatchID          *uint          `json:"batch_id" gorm:"index"`
	Batch            *ProductBatch  `json:"batch" gorm:"foreignKey:BatchID"`
	SerialID         *uint          `json:"serial_id" gorm:"index"`
	Serial           *ProductSerial `json:"serial" gorm:"foreignKey:SerialID"`
	SourceLocationID uint           `json:"source_location_id" gorm:"index;not null"`
	SourceLocation   Location       `json:"source_location" gorm:"foreignKey:SourceLocationID"`
	TargetLocationID uint           `json:"target_location_id" gorm:"index;not null"`
	TargetLocation   Location       `json:"target_location" gorm:"foreignKey:TargetLocationID"`
	Quantity         int            `json:"quantity" gorm:"default:1"`
	Status           int            `json:"status" gorm:"default:1"` // 1: 待移动, 2: 已完成
	Operator         string         `json:"operator" gorm:"size:50"` // 操作人
	Remark           string         `json:"remark" gorm:"size:500"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (InventoryMove) TableName() string {
	return "wms_inventory_move"
}

// InventoryAlert 库存预警
type InventoryAlert struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ProductID   uint           `json:"product_id" gorm:"index;not null"`
	Product     Product        `json:"product" gorm:"foreignKey:ProductID"`
	WarehouseID uint           `json:"warehouse_id" gorm:"index"`
	Warehouse   Warehouse      `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	AlertType   string         `json:"alert_type" gorm:"size:50"` // 预警类型：低库存、高库存、临期
	Threshold   int            `json:"threshold"`                  // 阈值
	CurrentQty  int            `json:"current_qty"`                // 当前数量
	Status      int            `json:"status" gorm:"default:1"`   // 1: 未处理, 2: 已处理
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (InventoryAlert) TableName() string {
	return "wms_inventory_alert"
}