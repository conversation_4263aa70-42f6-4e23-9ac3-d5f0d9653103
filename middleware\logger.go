package middleware

import (
	"bytes"
	"io"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 获取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			// 重置请求体，因为读取后会消耗
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建自定义响应写入器
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		// 处理请求
		c.Next()

		// 结束时间
		endTime := time.Now()
		latency := endTime.Sub(startTime)

		// 获取响应状态
		statusCode := c.Writer.Status()

		// 记录日志
		log.Printf("[%s] %s %s %d %v",
			c.Request.Method,
			c.Request.URL.Path,
			c.ClientIP(),
			statusCode,
			latency,
		)

		// 对于特定API或错误状态码，可以记录请求和响应体
		if statusCode >= 400 || shouldLogBody(c.Request.URL.Path) {
			log.Printf("Request Body: %s", string(requestBody))
			log.Printf("Response Body: %s", blw.body.String())
		}
	}
}

// bodyLogWriter 是一个自定义的响应写入器，用于捕获响应体
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 重写Write方法，同时写入原始响应和缓冲区
func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// shouldLogBody 判断是否应该记录请求和响应体
func shouldLogBody(path string) bool {
	// 这里可以根据需要定义需要记录详细日志的路径
	// 例如登录、重要操作等
	sensitivePaths := map[string]bool{
		"/api/v1/auth/login":    true,
		"/api/v1/auth/register": true,
	}

	return sensitivePaths[path]
}