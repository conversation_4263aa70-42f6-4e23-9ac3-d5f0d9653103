package models

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`    // 状态码，0表示成功，非0表示失败
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// PageInfo 分页信息
type PageInfo struct {
	Page      int `json:"page" form:"page" binding:"required,min=1"`           // 当前页码
	PageSize  int `json:"page_size" form:"page_size" binding:"required,min=1"` // 每页记录数
	Total     int `json:"total"`                                                  // 总记录数
	TotalPage int `json:"total_page"`                                             // 总页数
}

// PageRequest 分页请求
type PageRequest struct {
	Page     int `json:"page" form:"page" binding:"required,min=1"`           // 当前页码
	PageSize int `json:"page_size" form:"page_size" binding:"required,min=1"` // 每页记录数
}

// PageResponse 分页响应
type PageResponse struct {
	List     interface{} `json:"list"`      // 数据列表
	PageInfo PageInfo    `json:"page_info"` // 分页信息
}

// NewResponse 创建新的响应
func NewResponse(code int, message string, data interface{}) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}) Response {
	return NewResponse(0, "success", data)
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message string) Response {
	return NewResponse(code, message, nil)
}

// NewPageResponse 创建分页响应
func NewPageResponse(list interface{}, page, pageSize, total int) Response {
	totalPage := total / pageSize
	if total%pageSize > 0 {
		totalPage++
	}

	pageInfo := PageInfo{
		Page:      page,
		PageSize:  pageSize,
		Total:     total,
		TotalPage: totalPage,
	}

	return NewSuccessResponse(PageResponse{
		List:     list,
		PageInfo: pageInfo,
	})
}