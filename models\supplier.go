package models

import (
	"time"

	"gorm.io/gorm"
)

// Supplier 供应商模型
type Supplier struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Code         string         `json:"code" gorm:"size:50;uniqueIndex;not null"`
	Name         string         `json:"name" gorm:"size:100;not null"`
	ContactName  string         `json:"contact_name" gorm:"size:50"`
	ContactPhone string         `json:"contact_phone" gorm:"size:20"`
	Email        string         `json:"email" gorm:"size:100"`
	Address      string         `json:"address" gorm:"size:200"`
	Remark       string         `json:"remark" gorm:"size:500"`
	Status       int            `json:"status" gorm:"default:1"` // 1: 启用, 0: 禁用
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Supplier) TableName() string {
	return "wms_supplier"
}

// SupplierCreateRequest 供应商创建请求
type SupplierCreateRequest struct {
	Code         string `json:"code" binding:"required,max=50"`
	Name         string `json:"name" binding:"required,max=100"`
	ContactName  string `json:"contact_name" binding:"max=50"`
	ContactPhone string `json:"contact_phone" binding:"max=20"`
	Email        string `json:"email" binding:"omitempty,email,max=100"`
	Address      string `json:"address" binding:"max=200"`
	Remark       string `json:"remark" binding:"max=500"`
}

// SupplierUpdateRequest 供应商更新请求
type SupplierUpdateRequest struct {
	Name         string `json:"name" binding:"max=100"`
	ContactName  string `json:"contact_name" binding:"max=50"`
	ContactPhone string `json:"contact_phone" binding:"max=20"`
	Email        string `json:"email" binding:"omitempty,email,max=100"`
	Address      string `json:"address" binding:"max=200"`
	Remark       string `json:"remark" binding:"max=500"`
	Status       int    `json:"status" binding:"oneof=0 1"`
}