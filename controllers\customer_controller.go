package controllers

import (
	"net/http"
	"strconv"

	"wms-gin/middleware"
	"wms-gin/models"
	"wms-gin/services"

	"github.com/gin-gonic/gin"
)

// CustomerController 客户控制器
type CustomerController struct {
	customerService services.CustomerService
}

// NewCustomerController 创建客户控制器
func NewCustomerController() *CustomerController {
	return &CustomerController{}
}

// CreateCustomer 创建客户
func (ctrl *CustomerController) CreateCustomer(c *gin.Context) {
	var req models.CustomerCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	customer, err := ctrl.customerService.CreateCustomer(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "创建成功",
		"data":    customer,
	})
}

// UpdateCustomer 更新客户
func (ctrl *CustomerController) UpdateCustomer(c *gin.Context) {
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的客户ID"})
		return
	}

	var req models.CustomerUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	customer, err := ctrl.customerService.UpdateCustomer(uint(customerID), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data":    customer,
	})
}

// GetCustomerByID 根据ID获取客户
func (ctrl *CustomerController) GetCustomerByID(c *gin.Context) {
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的客户ID"})
		return
	}

	customer, err := ctrl.customerService.GetCustomerByID(uint(customerID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    customer,
	})
}

// GetCustomerByCode 根据编码获取客户
func (ctrl *CustomerController) GetCustomerByCode(c *gin.Context) {
	code := c.Param("code")

	customer, err := ctrl.customerService.GetCustomerByCode(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    customer,
	})
}

// ListCustomers 获取客户列表
func (ctrl *CustomerController) ListCustomers(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyword := c.Query("keyword")

	customers, total, err := ctrl.customerService.ListCustomers(page, pageSize, keyword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  customers,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// DeleteCustomer 删除客户
func (ctrl *CustomerController) DeleteCustomer(c *gin.Context) {
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的客户ID"})
		return
	}

	err = ctrl.customerService.DeleteCustomer(uint(customerID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

// RegisterRoutes 注册路由
func (ctrl *CustomerController) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api")
	{
		// 需要认证的接口
		auth := api.Group("/customer", middleware.JWTAuth())
		{
			auth.POST("", ctrl.CreateCustomer)
			auth.PUT("/:id", ctrl.UpdateCustomer)
			auth.GET("/:id", ctrl.GetCustomerByID)
			auth.GET("/code/:code", ctrl.GetCustomerByCode)
			auth.GET("/list", ctrl.ListCustomers)
			auth.DELETE("/:id", ctrl.DeleteCustomer)
		}
	}
}